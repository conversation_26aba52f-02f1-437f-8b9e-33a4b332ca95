// Import use cases
const FormatCurrencyUseCase = require('./domain/usecases/FormatCurrencyUseCase');
const GreetUseCase = require('./domain/usecases/GreetUseCase');
const GetSessionTokenUseCase = require('./domain/usecases/GetSessionTokenUseCase');
const GetFaceTecSessionTokenWithEkycTokenUseCase = require('./domain/usecases/GetFaceTecSessionTokenWithEkycTokenUseCase');

// Import repositories and data sources
const AuthRepository = require('./data/repositories/AuthRepository');
const AuthApiDataSource = require('./data/datasources/AuthApiDataSource');

// Import utilities
const { TokenStorage } = require('./infrastructure/utils');
const { UuidGenerator } = require('./infrastructure/utils');


// Import FaceTec service
const facetecService = require('./facetec');

// Create instances of data sources
const authApiDataSource = new AuthApiDataSource();

// Create instances of repositories
const authRepository = new AuthRepository(authApiDataSource);

// Create instances of use cases
const formatCurrencyUseCase = new FormatCurrencyUseCase();
const greetUseCase = new GreetUseCase();
const getSessionTokenUseCase = new GetSessionTokenUseCase(authRepository);
const getFaceTecSessionTokenWithEkycTokenUseCase = new GetFaceTecSessionTokenWithEkycTokenUseCase(authRepository);

// Import PhotoIDScanProcessor
const PhotoIDScanProcessor = require('./processors/PhotoIDScanProcessor');
const PhotoIDMatchProcessor = require('./processors/PhotoIDMatchProcessor');

/**
 * Format a currency amount
 * @param {number} amount - The amount to format
 * @param {string} currency - The currency code (e.g., 'USD')
 * @returns {string} - The formatted currency amount
 */
const formatCurrency = (amount, currency = 'USD') => {
  return formatCurrencyUseCase.execute(amount, currency);
};

/**
 * Generate a greeting message
 * @param {string} name - The name to greet
 * @returns {string} - The greeting message
 */
const greet = (name) => {
  return greetUseCase.execute(name);
};

/**
 * Get a session token from the eKYC authentication API
 * @param {Object} headers - Optional additional headers to include in the request
 * @param {boolean} storeToken - Whether to store the ekycToken in localStorage (default: true)
 * @returns {Promise<Object>} - The response data
 */
const getSessionToken = async (headers = {}, storeToken = true) => {
  try {
    // Store the session ID from headers if provided
    if (headers['X-Session-Id']) {
      TokenStorage.storeSessionId(headers['X-Session-Id']);
    }

    const sessionToken = await getSessionTokenUseCase.execute(headers);
    const responseData = sessionToken.toJSON();

    // Store the session ID from the response headers if available
    // Note: This would require modifying the API response to include the headers
    // For now, we'll use the session ID from the request headers

    // Store the ekycToken if requested
    if (storeToken) {
      const ekycToken = sessionToken.getEkycToken();
      if (ekycToken) {
        TokenStorage.storeEkycToken(ekycToken);
      }
    }

    return responseData;
  } catch (error) {
    console.error('Error getting session token:', error);
    throw error;
  }
};

/**
 * Get the stored eKYC token
 * @returns {string|null} - The stored eKYC token or null if not found
 */
const getStoredEkycToken = () => {
  return TokenStorage.getEkycToken();
};

/**
 * Remove the stored eKYC token
 * @returns {boolean} - True if the token was removed successfully
 */
const clearEkycToken = () => {
  return TokenStorage.removeEkycToken();
};

/**
 * Get a FaceTec session token using the stored eKYC token
 * @param {Object} headers - Optional additional headers to include in the request
 * @param {boolean} initializeFaceTecSdk - Whether to initialize FaceTec SDK with the response data (default: true)
 * @returns {Promise<Object>} - The response data with an additional property 'faceTecInitialized' indicating if FaceTec was initialized
 */
const getFaceTecSessionTokenWithEkycToken = async (headers = {}, initializeFaceTecSdk = true) => {
  try {
    // Get the stored session ID if available
    const storedSessionId = TokenStorage.getSessionId();
    if (storedSessionId && !headers['X-Session-Id']) {
      // Add the stored session ID to the headers if not already provided
      headers = {
        ...headers,
        'X-Session-Id': storedSessionId
      };
    }

    const sessionToken = await getFaceTecSessionTokenWithEkycTokenUseCase.execute(headers);
    const responseData = sessionToken.toJSON();

    // Add a property to track if FaceTec was initialized
    responseData.faceTecInitialized = false;


    // Initialize FaceTec SDK if requested and if the response contains the required data
    if (initializeFaceTecSdk &&
        responseData &&
        responseData.code === "CUS-KYC-1000" &&
        responseData.data &&
        responseData.data.deviceKey &&
        responseData.data.encryptionKey) {

      try {
        // Initialize FaceTec with the values from the response
        await facetecService.initializeFaceTec(
          responseData.data.deviceKey,
          responseData.data.encryptionKey
        );
        // console.log("Status: " + facetecService.loadFaceTecSDK.getStatus());
        console.log('FaceTec SDK initialized successfully');
        responseData.faceTecInitialized = true;
      } catch (initError) {
        // console.log("Status: " + facetecService.loadFaceTecSDK.getStatus());
        console.error('Error initializing FaceTec SDK:', initError);
        responseData.faceTecError = initError.message || 'Failed to initialize FaceTec SDK';
      }
    }

    return responseData;
  } catch (error) {
    console.error('Error getting FaceTec session token with eKYC token:', error);
    throw error;
  }
};

/**
 * Perform Photo ID Scan using FaceTec SDK
 * @param {Object} headers - Optional additional headers to include in the request
 * @param {string} deviceKey - The device key for the Photo ID Scan
 * @returns {Promise<Object>} - The scan result data
 */
const performPhotoIDScan = async (headers = {}, deviceKey = null, sessionTokenResponse = null) => {

  try {
    if (!deviceKey) {
      throw new Error('deviceKey parameter is required for Photo ID Scan');
    }

    if (!sessionTokenResponse.faceTecInitialized) {
      throw new Error('FaceTec SDK not initialized properly');
    }

    const FaceTecSDK = await facetecService.loadFaceTecSDK();
    FaceTecSDK.setResourceDirectory("/core-sdk/FaceTecSDK.js/resources");
    FaceTecSDK.setImagesDirectory("/core-sdk/FaceTec_images");

    const controller = {
      onComplete: (sessionResult, idScanResult, networkResponseStatus) => {
        return { sessionResult, idScanResult, networkResponseStatus };
      }
    };

    // ✅ prepare headers follow spec for PhotoIDScanProcessor
    const additionalHeaders = {
      'X-Session-Id': headers['X-Session-Id'] || sessionTokenResponse.data?.sessionId,
      'X-Ekyc-Token': sessionTokenResponse.data?.ekycToken || TokenStorage.getEkycToken(),
      'correlationid': headers.correlationid || require('./infrastructure/utils').UuidGenerator.getUniqueId()
    };

    const processor = new PhotoIDScanProcessor(
      sessionTokenResponse.data.sessionFaceTec,
      controller,
      deviceKey,
      additionalHeaders  // ✅ send additional headers
    );

    return new Promise((resolve, reject) => {
      controller.onComplete = (sessionResult, idScanResult, networkResponseStatus) => {
        if (processor.isSuccess()) {
          resolve({ sessionResult, idScanResult, networkResponseStatus });
        } else {
          reject(new Error('ID scan failed'));
        }
      };
    });
  } catch (error) {
    console.error('Error performing photo ID scan:', error);
    throw error;
  }
};

const performPhotoIDMatch = async (headers = {}, deviceKey = null, sessionTokenResponse = null, ocrResultsCallback = null) => {

  try {
    if (!deviceKey) {
      throw new Error('deviceKey parameter is required for Photo ID Scan');
    }

    if (!sessionTokenResponse.faceTecInitialized) {
      throw new Error('FaceTec SDK not initialized properly');
    }

    const FaceTecSDK = await facetecService.loadFaceTecSDK();
    FaceTecSDK.setResourceDirectory("/core-sdk/FaceTecSDK.js/resources");
    FaceTecSDK.setImagesDirectory("/core-sdk/FaceTec_images");

    const controller = {
      onComplete: (sessionResult, idScanResult, networkResponseStatus) => {
        return { sessionResult, idScanResult, networkResponseStatus };
      }
    };

    // ✅ prepare headers follow spec for PhotoIDScanProcessor
    const additionalHeaders = {
      'X-Session-Id': headers['X-Session-Id'] || sessionTokenResponse.data?.sessionId,
      'X-Ekyc-Token': sessionTokenResponse.data?.ekycToken || TokenStorage.getEkycToken(),
      'correlationid': headers.correlationid || require('./infrastructure/utils').UuidGenerator.getUniqueId()
    };

    const processor = new PhotoIDMatchProcessor(
      sessionTokenResponse.data.sessionFaceTec,
      controller,
      deviceKey,
      additionalHeaders,  // ✅ send additional headers
      ocrResultsCallback  // ✅ pass the callback to processor
    );

    return new Promise((resolve, reject) => {
      // Track if the overlay was shown and cancelled
      let overlayShownAndCancelled = false;

      // Store reference to the processor for overlay access
      processor._overlayShownAndCancelled = () => {
        overlayShownAndCancelled = true;
      };

      controller.onComplete = (sessionResult, idScanResult, networkResponseStatus) => {
        if (processor.isSuccess()) {
          resolve({ sessionResult, idScanResult, networkResponseStatus, processor });
        } else if (overlayShownAndCancelled) {
          // If overlay was shown and cancelled, resolve instead of reject
          resolve({ sessionResult, idScanResult, networkResponseStatus, cancelled: true, processor });
        } else {
          reject(new Error('ID scan failed'));
        }
      };
    });
  } catch (error) {
    console.error('Error performing photo ID scan:', error);
    throw error;
  }
};

/**
 * 1. Initialize eKYC SDK
 * @param {Object} options - Initialization options
 * @param {string} options.sessionId - Session ID for the eKYC session
 * @param {string} options.token - API token for authentication
 * @param {string} options.environment - Environment (development, staging, production)
 * @param {string} options.language - Language code (default: 'en')
 * @param {Function} options.initCallback - Optional initialization callback
 * @returns {Promise<Object>} Initialization result
 */
const initEkyc = async (options = {}) => {
  const {
    sessionId,
    token,
    environment = 'development',
    language = 'en',
    initCallback
  } = options;

  // Validate required parameters
  if (!sessionId) {
    throw new Error('sessionId is required for eKYC initialization');
  }
  if (!token) {
    throw new Error('token is required for eKYC initialization');
  }

  try {
    console.log('🚀 Initializing eKYC SDK with sessionId:', sessionId);

    // Generate and store device ID if not exists
    const { UuidGenerator } = require('./infrastructure/utils');
    let deviceId = TokenStorage.getToken('ekyc_device_id');
    if (!deviceId) {
      deviceId = UuidGenerator.getUniqueId();
      TokenStorage.storeToken('ekyc_device_id', deviceId);
    }

    // Store session information
    TokenStorage.storeToken('ekyc_session_id', sessionId);
    TokenStorage.storeToken('ekyc_api_token', token);
    TokenStorage.storeToken('ekyc_environment', environment);
    TokenStorage.storeToken('ekyc_language', language);

    // Prepare headers for session token request
    const sessionHeaders = {
      'Authorization': `Bearer ${token}`,
      'X-Session-Id': sessionId,
      'X-Ekyc-Device-Info': `browser|${deviceId}|${typeof window !== 'undefined' ? window.location.origin : 'unknown'}|${language}|${language.toUpperCase()}`
    };

    // Get session token
    console.log('📡 Getting session token...');
    const sessionTokenResponse = await getSessionToken(sessionHeaders, true);

    // Get FaceTec session token and initialize
    console.log('🎭 Getting FaceTec session token and initializing...');
    const faceTecResponse = await getFaceTecSessionTokenWithEkycToken(sessionHeaders, true);

    const result = {
      success: true,
      sessionToken: sessionTokenResponse,
      faceTecToken: faceTecResponse,
      environment,
      language,
      sessionId,
      initialized: true,
      faceTecInitialized: faceTecResponse.faceTecInitialized || false
    };

    console.log('✅ eKYC SDK initialized successfully');

    // Call initialization callback if provided
    if (initCallback && typeof initCallback === 'function') {
      initCallback(result);
    }

    return result;
  } catch (error) {
    console.error('❌ Error initializing eKYC SDK:', error);
    const errorResult = {
      success: false,
      error: error.message || 'Failed to initialize eKYC SDK',
      environment,
      language,
      sessionId,
      initialized: false
    };

    if (initCallback && typeof initCallback === 'function') {
      initCallback(errorResult);
    }

    throw error;
  }
};

/**
 * 2. OCR ID Card scanning
 * @param {Object} options - OCR options
 * @param {boolean} options.checkExpiredIdCard - Check if ID card is expired (default: true)
 * @param {boolean} options.checkDopa - Check against DOPA database (default: false)
 * @param {boolean} options.enableConfirmInfo - Enable confirmation screen (default: true)
 * @param {Function} options.callback - Result callback function
 * @returns {Promise<Object>} OCR results
 */
const ocrIdCard = async (options = {}) => {
  const {
    checkExpiredIdCard = true,
    checkDopa = false,
    enableConfirmInfo = true,
    callback
  } = options;

  try {
    console.log('📄 Starting OCR ID Card scan...');

    // Get stored session information
    const sessionId = TokenStorage.getToken('ekyc_session_id');
    const deviceId = TokenStorage.getToken('ekyc_device_id') || 'unknown';

    if (!sessionId) {
      throw new Error('eKYC SDK not initialized. Call initEkyc() first.');
    }

    // Prepare headers
    const headers = {
      'X-Session-Id': sessionId,
      'X-Ekyc-Device-Info': `browser|${deviceId}`,
      'X-Ekyc-Token': TokenStorage.getToken('ekyc_token')
    };

    // Ensure FaceTec is initialized by getting session token
    console.log('🎭 Ensuring FaceTec SDK is initialized...');
    const faceTecResponse = await getFaceTecSessionTokenWithEkycToken(headers, true);

    if (!faceTecResponse.faceTecInitialized) {
      throw new Error('FaceTec SDK not properly initialized');
    }

    // Perform ID scan using existing logic with callback integration
    console.log('🔍 Performing ID scan...');
    const scanResult = await performPhotoIDScan(headers, deviceId, faceTecResponse, ocrResultsCallback);

    // Extract callback result data for standardized return structure
    const callbackResult = scanResult.processor ? scanResult.processor.getCallbackResult() : null;

    // Create standardized result structure matching callback signature
    const result = {
      success: callbackResult ? callbackResult.success : true,
      description: callbackResult ? callbackResult.description : "OCR ID Card scan completed successfully",
      userOcrValue: callbackResult ? callbackResult.userOcrValue : null,
      userConfirmedValue: callbackResult ? callbackResult.userConfirmedValue : null,
      dopaResult: callbackResult ? callbackResult.dopaResult : null
    };

    console.log('✅ OCR ID Card scan completed');
    console.log('📊 Standardized result structure:', {
      success: result.success,
      description: result.description,
      hasUserOcrValue: !!result.userOcrValue,
      hasUserConfirmedValue: !!result.userConfirmedValue
    });

    // Call legacy callback if provided
    if (callback && typeof callback === 'function') {
      callback(result);
    }

    return result;
  } catch (error) {
    console.error('❌ Error performing OCR ID card scan:', error);

    // Create standardized error result structure
    const errorResult = {
      success: false,
      description: error.message || 'Unable to Process',
      userOcrValue: null,
      userConfirmedValue: null,
      dopaResult: null,
    };

    if (callback && typeof callback === 'function') {
      callback(errorResult);
    }

    throw error;
  }
};

/**
 * 3. OCR ID Card with facial verification
 * @param {Object} options - OCR and face verification options
 * @param {boolean} options.checkExpiredIdCard - Check if ID card is expired (default: true)
 * @param {boolean} options.checkDopa - Check against DOPA database (default: false)
 * @param {boolean} options.enableConfirmInfo - Enable confirmation screen (default: true)
 * @param {Function} options.ocrResultsCallback - Callback for OCR and face verification results
 * @returns {Promise<Object>} Combined OCR and face verification results
 */
const ocrIdCardVerifyByFace = async (options = {}) => {
  const {
    checkExpiredIdCard = true,
    checkDopa = false,
    enableConfirmInfo = true,
    callback,
    ocrResultsCallback
  } = options;

  try {
    console.log('📄 Starting OCR ID Card with facial verification scan...');

    // Get stored session information
    const sessionId = TokenStorage.getToken('ekyc_session_id');
    const deviceId = TokenStorage.getToken('ekyc_device_id') || 'unknown';

    if (!sessionId) {
      throw new Error('eKYC SDK not initialized. Call initEkyc() first.');
    }

    // Prepare headers
    const headers = {
      'X-Session-Id': sessionId,
      'X-Ekyc-Device-Info': `browser|${deviceId}`,
      'X-Ekyc-Token': TokenStorage.getToken('ekyc_token')
    };

    // Ensure FaceTec is initialized by getting session token
    console.log('🎭 Ensuring FaceTec SDK is initialized...');
    const faceTecResponse = await getFaceTecSessionTokenWithEkycToken(headers, true);

    if (!faceTecResponse.faceTecInitialized) {
      throw new Error('FaceTec SDK not properly initialized');
    }

    // Perform ID scan with face verification using existing logic with callback integration
    console.log('🔍 Performing ID scan with face verification...');
    const scanResult = await performPhotoIDMatch(headers, deviceId, faceTecResponse, ocrResultsCallback);

    // Extract callback result data for standardized return structure
    console.log('🔍 DEBUG: Extracting callback result from scanResult');
    console.log('📊 DEBUG: scanResult structure:', {
      hasProcessor: !!scanResult.processor,
      scanResultKeys: Object.keys(scanResult),
      processorType: scanResult.processor ? scanResult.processor.constructor.name : 'N/A'
    });

    const callbackResult = scanResult.processor ? scanResult.processor.getCallbackResult() : null;

    console.log('📋 DEBUG: Retrieved callback result:', {
      hasCallbackResult: !!callbackResult,
      callbackResultKeys: callbackResult ? Object.keys(callbackResult) : [],
      callbackResult: callbackResult
    });

    // Also check the raw scan result for OCR data
    console.log('🔍 DEBUG: Raw scan result analysis:', {
      sessionResult: scanResult.sessionResult ? 'present' : 'missing',
      idScanResult: scanResult.idScanResult ? 'present' : 'missing',
      networkResponseStatus: scanResult.networkResponseStatus,
      scanResultData: scanResult
    });

    // Extract userOcrValue - prioritize callback result, then try direct extraction
    let extractedUserOcrValue = null;

    // PRIORITY 1: Use userOcrValue from callback result if available (most reliable)
    if (callbackResult && callbackResult.userOcrValue) {
      console.log('✅ DEBUG: Using userOcrValue from callback result');
      extractedUserOcrValue = callbackResult.userOcrValue;
    }
    // PRIORITY 2: Try to extract from processor if callback result doesn't have it
    else if (scanResult.processor && scanResult.processor.extractOcrFromApiResponse && scanResult.idScanResult) {
      console.log('🔍 DEBUG: Extracting userOcrValue from scan result using processor');
      extractedUserOcrValue = scanResult.processor.extractOcrFromApiResponse(scanResult.idScanResult);
    }
    // PRIORITY 3: Check if scan result itself has userOcrValue
    else if (scanResult.idScanResult && scanResult.idScanResult.userOcrValue) {
      console.log('✅ DEBUG: Using userOcrValue directly from scan result');
      extractedUserOcrValue = scanResult.idScanResult.userOcrValue;
    }

    console.log('📋 DEBUG: Final extracted userOcrValue:', {
      hasValue: !!extractedUserOcrValue,
      valueKeys: extractedUserOcrValue ? Object.keys(extractedUserOcrValue) : [],
      value: extractedUserOcrValue
    });

    // Create standardized result structure matching the required callback signature
    const result = {
      success: callbackResult ? callbackResult.success : true,
      description: callbackResult ? callbackResult.description : "OCR ID Card with face verification completed successfully",
      userOcrValue: extractedUserOcrValue,
      userConfirmedValue: callbackResult ? callbackResult.userConfirmedValue : null,
      dopaResult: callbackResult ? callbackResult.dopaResult : null
    };

    console.log('✅ OCR ID Card with face verification completed');
    console.log('📊 Standardized result structure:', {
      success: result.success,
      description: result.description,
      hasUserOcrValue: !!result.userOcrValue,
      hasUserConfirmedValue: !!result.userConfirmedValue
    });

    // Call legacy callback if provided
    if (callback && typeof callback === 'function') {
      callback(result);
    }

    return result;
  } catch (error) {
    console.error('❌ Error performing OCR ID card with face verification:', error);

    // Create standardized error result structure
    const errorResult = {
      success: false,
      description: error.message || 'Failed to perform OCR ID card with face verification',
      userOcrValue: null,
      userConfirmedValue: null,
      dopaResult: null
    };

    if (callback && typeof callback === 'function') {
      callback(errorResult);
    }

    throw error;
  }
};

/**
 * 4. NDID digital identity verification
 * @param {Object} options - NDID verification options
 * @param {string} options.identifierType - Type of identifier (citizenId, passport, etc.)
 * @param {string} options.identifierValue - The identifier value
 * @param {string} options.serviceId - Service ID for NDID verification
 * @param {Function} options.ndidVerificationCallback - Callback for NDID verification results
 * @returns {Promise<Object>} NDID verification results
 */
const ndidVerification = async (options = {}) => {
  const {
    identifierType,
    identifierValue,
    serviceId,
    ndidVerificationCallback
  } = options;

  // Validate required parameters
  if (!identifierType) {
    throw new Error('identifierType is required for NDID verification');
  }
  if (!identifierValue) {
    throw new Error('identifierValue is required for NDID verification');
  }
  if (!serviceId) {
    throw new Error('serviceId is required for NDID verification');
  }

  try {
    console.log('🆔 Starting NDID verification...');

    // Get stored session information
    const sessionId = TokenStorage.getToken('ekyc_session_id');
    const deviceId = TokenStorage.getToken('ekyc_device_id') || 'unknown';

    if (!sessionId) {
      throw new Error('eKYC SDK not initialized. Call initEkyc() first.');
    }

    // Simulate NDID verification process
    // In a real implementation, this would call the actual NDID verification API
    const verificationId = `ndid_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 2000));

    const result = {
      success: true,
      ndidVerified: true,
      identifierType,
      identifierValue,
      serviceId,
      sessionId,
      verificationId,
      timestamp: new Date().toISOString(),
      // Simulated NDID response
      ndidResponse: {
        status: 'verified',
        confidence: 0.95,
        details: {
          identityConfirmed: true,
          documentValid: true,
          biometricMatch: true
        }
      }
    };

    console.log('✅ NDID verification completed successfully');

    // Call callback if provided
    if (ndidVerificationCallback && typeof ndidVerificationCallback === 'function') {
      ndidVerificationCallback(result);
    }

    return result;
  } catch (error) {
    console.error('❌ Error performing NDID verification:', error);
    const errorResult = {
      success: false,
      error: error.message || 'Failed to perform NDID verification',
      identifierType,
      identifierValue,
      serviceId
    };

    if (ndidVerificationCallback && typeof ndidVerificationCallback === 'function') {
      ndidVerificationCallback(errorResult);
    }

    throw error;
  }
};

/**
 * 5. Facial liveness detection
 * @param {Object} options - Liveness check options
 * @param {Function} options.livenessCheckCallback - Callback for liveness check results
 * @returns {Promise<Object>} Liveness check results
 */
const livenessCheck = async (options = {}) => {
  const {
    livenessCheckCallback
  } = options;

  try {
    console.log('👁️ Starting liveness check...');

    // Get stored session information
    const sessionId = TokenStorage.getToken('ekyc_session_id');
    const deviceId = TokenStorage.getToken('ekyc_device_id') || 'unknown';

    if (!sessionId) {
      throw new Error('eKYC SDK not initialized. Call initEkyc() first.');
    }

    // Prepare headers
    const headers = {
      'X-Session-Id': sessionId,
      'X-Ekyc-Device-Info': `browser|${deviceId}`
    };

    // Get FaceTec session token if not available
    console.log('🎭 Ensuring FaceTec SDK is initialized...');
    const faceTecResponse = await getFaceTecSessionTokenWithEkycToken(headers, true);

    if (!faceTecResponse.faceTecInitialized) {
      throw new Error('FaceTec SDK not properly initialized');
    }

    // Prepare additional headers for the API request
    const additionalHeaders = {
      'X-Device-Key': deviceId || faceTecResponse.data?.deviceKey,
      'X-Session-Id': sessionId,
      'X-Ekyc-Token': faceTecResponse.data?.ekycToken || TokenStorage.getEkycToken(),
      'X-Tid': UuidGenerator.getUniqueId(),
      'correlationid': UuidGenerator.getUniqueId()
    };

    // Create a controller for the LivenessCheckProcessor
    const controller = {
      onComplete: (sessionResult, networkResponseStatus) => {
        return { sessionResult, networkResponseStatus };
      }
    };

    // Create a promise to handle the liveness check result
    const livenessPromise = new Promise((resolve, reject) => {
      // Override the onComplete method to handle the result
      controller.onComplete = (sessionResult, networkResponseStatus) => {
        // Check if the processor was successful instead of relying only on sessionResult status
        if (processor.isSuccess()) {
          resolve({ sessionResult, networkResponseStatus });
        } else if (sessionResult && sessionResult.status === FaceTecSDK.FaceTecSessionStatus.SessionCompletedSuccessfully) {
          resolve({ sessionResult, networkResponseStatus });
        } else {
          reject(new Error('Liveness check failed or was cancelled'));
        }
      };
    });

    // Create and start the LivenessCheckProcessor
    console.log('🔍 Creating LivenessCheckProcessor...');
    const LivenessCheckProcessor = require('./processors/LivenessCheckProcessor');
    const processor = new LivenessCheckProcessor(
      faceTecResponse.data.sessionFaceTec,
      controller,
      additionalHeaders['X-Device-Key'],
      additionalHeaders
    );

    // Wait for the liveness check to complete
    console.log('⏳ Waiting for liveness check to complete...');
    const livenessResult = await livenessPromise;

    // Process the result
    const result = {
      success: processor.isSuccess(),
      liveness: {
        sessionId: livenessResult.sessionResult?.sessionId || `liveness_${Date.now()}`,
        livenessScore: 1.0, // FaceTec doesn't provide a score, but it's either pass or fail
        isLive: processor.isSuccess(),
        confidence: processor.isSuccess() ? 'high' : 'low',
        timestamp: new Date().toISOString()
      },
      sessionId,
      deviceId,
      faceTecInitialized: true,
      rawResult: livenessResult
    };

    console.log('✅ Liveness check completed successfully:', result.success);

    // Call callback if provided
    if (livenessCheckCallback && typeof livenessCheckCallback === 'function') {
      livenessCheckCallback({
        responseCode: result.success ? 'CUS-KYC-1000' : 'ERROR',
        description: result.success ? 'Liveness check successful' : 'Unable to Process',
        success: result.success,
        data: result
      });
    }

    return result;
  } catch (error) {
    console.error('❌ Error performing liveness check:', error);
    const errorResult = {
      success: false,
      error: error.message || 'Failed to perform liveness check'
    };

    if (livenessCheckCallback && typeof livenessCheckCallback === 'function') {
      livenessCheckCallback({
        responseCode: 'ERROR',
        responseDescription: error.message || 'Failed to perform liveness check',
        success: false,
        error: error.message
      });
    }

    throw error;
  }
};

// Export the functions
module.exports = {
  formatCurrency,
  greet,
  getSessionToken,
  getStoredEkycToken,
  clearEkycToken,
  getFaceTecSessionTokenWithEkycToken,
  performPhotoIDScan,
  // New 5 main SDK functions
  initEkyc,
  ocrIdCard,
  ocrIdCardVerifyByFace,
  ndidVerification,
  livenessCheck
};
