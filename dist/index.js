!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("index",[],t):"object"==typeof exports?exports.index=t():e.index=t()}(this,(()=>{return e={0:(e,t,s)=>{const a=s(216),o=s(599),n=s(719);e.exports=class{constructor(){this.faceTecRepository=new a}async execute({idScanResult:e,deviceKey:t,additionalHeaders:s={},onProgress:a=null}){const o=performance.now();try{n.logMessage("Starting PostIDScanOnlyUseCase execution"),n.logMessage("Preparing scan data...");const r=this.prepareScanData(e);n.logData("Scan Data Keys",Object.keys(r)),n.logMessage("Preparing headers...");const i=this.prepareHeaders(e,t,s);n.logData("Request Headers",Object.keys(i)),n.logMessage("Validating scan data..."),this.faceTecRepository.validateScanData(r),n.logSuccess("Scan data validation passed"),n.logMessage("Submitting to repository...");const c=await this.faceTecRepository.submitIDScan(r,i,a);n.logMessage("Processing response...");const l=this.processResponse(c),d=performance.now();return n.logPerformance("PostIDScanOnlyUseCase.execute",o,d),n.logSuccess(`UseCase completed successfully: ${l.success}`),l}catch(e){const t=performance.now();throw n.logPerformance("PostIDScanOnlyUseCase.execute (failed)",o,t),n.logError("PostIDScanOnlyUseCase - execute error",e),e}}prepareScanData(e){const t={idScan:e.idScan,enableConfirmInfo:!0};return e.frontImages&&e.frontImages[0]&&(t.idScanFrontImage=e.frontImages[0]),e.backImages&&e.backImages[0]&&(t.idScanBackImage=e.backImages[0]),t}prepareHeaders(e,t,s){const a={};return t&&(a["X-Device-Key"]=t),e.sessionId&&(a["X-User-Agent"]=FaceTecSDK.createFaceTecAPIUserAgentString(e.sessionId)),s.Authorization&&(a.Authorization=s.Authorization),s["X-Session-Id"]&&(a["X-Session-Id"]=s["X-Session-Id"]),s["X-Ekyc-Token"]&&(a["X-Ekyc-Token"]=s["X-Ekyc-Token"]),s.correlationid&&(a.correlationid=s.correlationid),a["X-Tid"]=o.getUniqueId(),a}processResponse(e){let t=null;if(e.originalResponse&&e.originalResponse.data&&e.originalResponse.data.documentData)try{const s=JSON.parse(e.originalResponse.data.documentData);n.logData("Parsed documentData",s),t=this.extractOcrDataFromDocumentData(s),t?n.logData("Extracted OCR Data",Object.keys(t)):n.logMessage("No valid OCR fields found in documentData")}catch(e){n.logError("Failed to parse documentData JSON:",e),n.logMessage("No OCR data found in response")}else n.logMessage("No documentData found in response");return{success:!0===e.wasProcessed&&!1===e.error,scanResultBlob:e.scanResultBlob,originalResponse:e.originalResponse,errorMessage:e.errorMessage,userOcrValue:t}}extractOcrDataFromDocumentData(e){if(!e||!e.scannedValues||!e.scannedValues.groups)return n.logMessage("Invalid documentData structure"),null;const t={nationalId:null,titleTh:null,firstNameTh:null,middleNameTh:null,lastNameTh:null,titleEn:null,firstNameEn:null,middleNameEn:null,lastNameEn:null,dateOfBirth:null,dateOfIssue:null,dateOfExpiry:null,laserId:null},s={idNumber:"nationalId",firstName:"_rawFirstName",fullname:"_rawFullname",fullName:"_rawFullname",lastName:"lastNameEn",dateOfBirth:"dateOfBirth",dateOfIssue:"dateOfIssue",dateOfExpiration:"dateOfExpiry",customField1:"laserId"},a={_rawFirstName:null,_rawFullname:null};return e.scannedValues.groups.forEach((e=>{e.fields&&Array.isArray(e.fields)&&e.fields.forEach((e=>{if(e.fieldKey&&e.value&&s[e.fieldKey]){const o=s[e.fieldKey];o.startsWith("_raw")?(a[o]=e.value,n.logData(`Stored raw ${e.fieldKey}`,e.value)):(t[o]=e.value,n.logData(`Mapped ${e.fieldKey} -> ${o}`,e.value))}}))})),a._rawFullname&&this.parseThaiNameFields(a._rawFullname,t),a._rawFirstName&&this.parseEnglishNameFields(a._rawFirstName,t),this.convertDateFields(t),this.addFieldEditabilityMetadata(t),Object.values(t).some((e=>null!==e))?t:(n.logMessage("No valid OCR fields found in documentData structure"),null)}parseThaiNameFields(e,t){if(!e||"string"!=typeof e)return void n.logMessage("Invalid fullname for Thai parsing");const s=e.trim().split(/\s+/);n.logData("Thai name parts",s),s.length>=1&&(t.titleTh=s[0],n.logData("Extracted titleTh",t.titleTh)),s.length>=2&&(t.firstNameTh=s[1],n.logData("Extracted firstNameTh",t.firstNameTh)),s.length>=3&&(t.lastNameTh=s.slice(2).join(" "),n.logData("Extracted lastNameTh",t.lastNameTh))}parseEnglishNameFields(e,t){if(!e||"string"!=typeof e)return void n.logMessage("Invalid firstName for English parsing");const s=e.trim().split(/\s+/);n.logData("English name parts",s),s.length>=1&&(t.titleEn=s[0],n.logData("Extracted titleEn",t.titleEn)),s.length>=2&&(t.firstNameEn=s.slice(1).join(" "),n.logData("Extracted firstNameEn",t.firstNameEn))}convertDateFields(e){["dateOfBirth","dateOfIssue","dateOfExpiry"].forEach((t=>{if(e[t]){const s=this.convertThaiDateFormat(e[t]);s?(n.logData(`Converted ${t}`,`${e[t]} -> ${s}`),e[t]=s):n.logError(`Failed to convert date field ${t}`,e[t])}}))}convertThaiDateFormat(e){if(!e||"string"!=typeof e)return null;try{const t={"ม.ค.":"01",มกราคม:"01","ม.ค":"01","ก.พ.":"02",กุมภาพันธ์:"02","ก.พ":"02","มี.ค.":"03",มีนาคม:"03","มี.ค":"03","เม.ย.":"04",เมษายน:"04","เม.ย":"04","พ.ค.":"05",พฤษภาคม:"05","พ.ค":"05","มิ.ย.":"06",มิถุนายน:"06","มิ.ย":"06","ก.ค.":"07",กรกฎาคม:"07","ก.ค":"07","ส.ค.":"08",สิงหาคม:"08","ส.ค":"08","ก.ย.":"09",กันยายน:"09","ก.ย":"09","ต.ค.":"10",ตุลาคม:"10","ต.ค":"10","พ.ย.":"11",พฤศจิกายน:"11","พ.ย":"11","ธ.ค.":"12",ธันวาคม:"12","ธ.ค":"12"},s=e.trim();if(/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(s))return n.logData("Date already in standard format",s),s;const a=s.split(/\s+/);if(a.length>=3){const s=a[0].padStart(2,"0"),o=a[1],r=a[2],i=t[o];if(i&&/^\d{4}$/.test(r)){const t=`${s}/${i}/${r}`;return n.logData("Thai date conversion successful",`${e} -> ${t}`),t}}const o=s.split("/");if(3===o.length){const s=o[0].padStart(2,"0"),a=o[1],r=o[2],i=t[a];if(i&&/^\d{4}$/.test(r)){const t=`${s}/${i}/${r}`;return n.logData("Thai date conversion (slash format) successful",`${e} -> ${t}`),t}}return n.logError("Unable to parse Thai date format",e),null}catch(e){return n.logError("Error converting Thai date format",e),null}}addFieldEditabilityMetadata(e){const t=["titleTh","firstNameTh","lastNameTh","titleEn","firstNameEn","lastNameEn","dateOfBirth","dateOfIssue","dateOfExpiry"],s=["nationalId","laserId"];e._fieldMetadata={editableFields:t,readOnlyFields:s,dateFields:["dateOfBirth","dateOfIssue","dateOfExpiry"],requiredFields:["nationalId","titleTh","firstNameTh","lastNameTh","dateOfBirth","dateOfExpiry"],optionalFields:["middleNameTh","middleNameEn","titleEn","firstNameEn","lastNameEn","dateOfIssue","laserId"]},Object.keys(e).forEach((a=>{"_fieldMetadata"!==a&&null!==e[a]&&(e[`_${a}Properties`]={isEditable:t.includes(a),isReadOnly:s.includes(a),isDate:["dateOfBirth","dateOfIssue","dateOfExpiry"].includes(a),isRequired:["nationalId","titleTh","firstNameTh","lastNameTh","dateOfBirth","dateOfExpiry"].includes(a),originalValue:e[a],hasBeenModified:!1})})),n.logData("Added field editability metadata",{editableCount:t.length,readOnlyCount:s.length,dateFieldCount:3})}}},103:e=>{e.exports=class{constructor(e){this.data=e}getToken(){return this.data?.token||null}getEkycToken(){return this.data?.data?.ekycToken?this.data.data.ekycToken:this.data?.ekycToken||null}getExpiresAt(){return this.data?.expiresAt||null}getCode(){return this.data?.code||null}getDescription(){return this.data?.description||null}isValid(){return!!this.getEkycToken()}toJSON(){return this.data}}},161:(e,t,s)=>{const a=s(103);e.exports=class{constructor(e){this.authApiDataSource=e}async getSessionToken(e={}){const t=await this.authApiDataSource.getSessionToken(e);return new a(t)}async getFaceTecSessionTokenWithEkycToken(e={}){const t=await this.authApiDataSource.getFaceTecSessionTokenWithEkycToken(e);return new a(t)}}},189:e=>{e.exports=class{constructor(e,t="USD"){this.amount=e,this.currencyCode=t}format(){return new Intl.NumberFormat("en-US",{style:"currency",currency:this.currencyCode}).format(this.amount)}getAmount(){return this.amount}getCurrencyCode(){return this.currencyCode}}},216:(e,t,s)=>{const a=s(518),o=s(405),n=s(489),r=s(777);e.exports=class{constructor(){this.idScanDataSource=new a,this.idScanFrontDataSource=new o,this.idScanBackDataSource=new n,this.livenessCheckDataSource=new r}async submitIDScan(e,t={},s=null){try{return await this.idScanDataSource.postIDScanOnly(e,t,s)}catch(e){throw console.error("FaceTecRepository - submitIDScan error:",e),e}}async submitIDScanFront(e,t={},s=null){const a=performance.now();try{console.log("=== FACETEC REPOSITORY - FRONT ID SCAN START ==="),this.logFrontScanRequest(e,t),this.validateAndLogFrontScanData(e),console.log("📤 Calling front data source...");const o=await this.idScanFrontDataSource.postIDScanFront(e,t,s);return this.logFrontScanResponse(o,a),console.log("=== FACETEC REPOSITORY - FRONT ID SCAN SUCCESS ==="),o}catch(e){const t=performance.now()-a;throw console.error("=== FACETEC REPOSITORY - FRONT ID SCAN ERROR ==="),console.error("❌ Error Type:",e.constructor.name),console.error("❌ Error Message:",e.message),console.error("❌ Error Stack:",e.stack),console.error("⏱️ Duration before error:",`${t.toFixed(2)}ms`),e.response&&(console.error("📥 Error Response Status:",e.response.status),console.error("📥 Error Response Headers:",e.response.headers),console.error("📥 Error Response Data:",e.response.data)),e.request&&console.error("📤 Failed Request Details:",{url:e.request.url||"Unknown URL",method:e.request.method||"Unknown Method",timeout:e.request.timeout||"No timeout set"}),console.error("=== END FRONT ID SCAN ERROR LOG ==="),e}}async submitIDScanBack(e,t={},s=null){const a=performance.now();try{console.log("=== FACETEC REPOSITORY - BACK ID SCAN START ==="),this.logBackScanRequest(e,t),this.validateAndLogBackScanData(e),console.log("📤 Calling back data source...");const o=await this.idScanBackDataSource.postIDScanBack(e,t,s);return this.logBackScanResponse(o,a),console.log("=== FACETEC REPOSITORY - BACK ID SCAN SUCCESS ==="),o}catch(e){const t=performance.now()-a;throw console.error("=== FACETEC REPOSITORY - BACK ID SCAN ERROR ==="),console.error("❌ Error Type:",e.constructor.name),console.error("❌ Error Message:",e.message),console.error("❌ Error Stack:",e.stack),console.error("⏱️ Duration before error:",`${t.toFixed(2)}ms`),e.response&&(console.error("📥 Error Response Status:",e.response.status),console.error("📥 Error Response Headers:",e.response.headers),console.error("📥 Error Response Data:",e.response.data)),e.request&&console.error("📤 Failed Request Details:",{url:e.request.url||"Unknown URL",method:e.request.method||"Unknown Method",timeout:e.request.timeout||"No timeout set"}),console.error("=== END BACK ID SCAN ERROR LOG ==="),e}}async submitLivenessCheck(e,t={},s=null){try{return await this.livenessCheckDataSource.postLivenessCheck(e,t,s)}catch(e){throw console.error("FaceTecRepository - submitLivenessCheck error:",e),e}}validateScanData(e){if(!e)throw new Error("Scan data is required");if(!e.idScan)throw new Error("ID scan data is required");return!0}validateFrontScanData(e){return this.idScanFrontDataSource.validateFrontScanData(e)}validateBackScanData(e){return this.idScanBackDataSource.validateBackScanData(e)}validateLivenessData(e){if(!e)throw new Error("Liveness data is required");if(!e.faceScan)throw new Error("Face scan data is required");return!0}logFrontScanRequest(e,t){console.log("📤 FRONT SCAN REQUEST DETAILS:"),console.log("🎯 Target Endpoint: /api/match-3d-2d-idscan/front"),console.log("📋 Request Headers:"),Object.keys(t).forEach((e=>{const s=t[e];e.toLowerCase().includes("token")||e.toLowerCase().includes("auth")?console.log(`  ${e}: ${this.maskSensitiveData(s)}`):console.log(`  ${e}: ${s}`)})),this.logScanDataStructure(e,"FRONT")}logBackScanRequest(e,t){console.log("📤 BACK SCAN REQUEST DETAILS:"),console.log("🎯 Target Endpoint: /api/match-3d-2d-idscan/back"),console.log("📋 Request Headers:"),Object.keys(t).forEach((e=>{const s=t[e];e.toLowerCase().includes("token")||e.toLowerCase().includes("auth")?console.log(`  ${e}: ${this.maskSensitiveData(s)}`):console.log(`  ${e}: ${s}`)})),this.logScanDataStructure(e,"BACK")}validateAndLogFrontScanData(e){console.log("🔍 FRONT SCAN DATA VALIDATION:");const t={hasIdScan:!!e?.idScan,hasFrontImage:!!e?.idScanFrontImage,hasBackImage:!!e?.idScanBackImage,hasEnableConfirmInfo:void 0!==e?.enableConfirmInfo,enableConfirmInfoValue:e?.enableConfirmInfo};console.log("✅ Validation Results:",t);const s=[];t.hasIdScan||s.push("❌ Missing idScan data"),t.hasFrontImage||s.push("❌ Missing idScanFrontImage (required for front scan)"),t.hasBackImage&&console.log("ℹ️ Back image present in front scan request (may be intentional)"),s.length>0?(console.error("🚨 VALIDATION ISSUES FOUND:"),s.forEach((e=>console.error(e)))):console.log("✅ All required fields present for front scan");try{this.validateFrontScanData(e),console.log("✅ Data source validation passed")}catch(e){throw console.error("❌ Data source validation failed:",e.message),e}}validateAndLogBackScanData(e){console.log("🔍 BACK SCAN DATA VALIDATION:");const t={hasIdScan:!!e?.idScan,hasFrontImage:!!e?.idScanFrontImage,hasBackImage:!!e?.idScanBackImage,hasEnableConfirmInfo:void 0!==e?.enableConfirmInfo,enableConfirmInfoValue:e?.enableConfirmInfo};console.log("✅ Validation Results:",t);const s=[];t.hasIdScan||s.push("❌ Missing idScan data"),t.hasBackImage||s.push("❌ Missing idScanBackImage (required for back scan)"),t.hasFrontImage||console.log("ℹ️ Front image missing in back scan request (may be optional)"),s.length>0?(console.error("🚨 VALIDATION ISSUES FOUND:"),s.forEach((e=>console.error(e)))):console.log("✅ All required fields present for back scan");try{this.validateBackScanData(e),console.log("✅ Data source validation passed")}catch(e){throw console.error("❌ Data source validation failed:",e.message),e}}logScanDataStructure(e,t){if(console.log(`📊 ${t} SCAN DATA STRUCTURE:`),!e)return void console.error("❌ Scan data is null or undefined");const s={keys:Object.keys(e),hasIdScan:!!e.idScan,hasIdScanFrontImage:!!e.idScanFrontImage,hasIdScanBackImage:!!e.idScanBackImage,enableConfirmInfo:e.enableConfirmInfo};console.log("📋 Basic Structure:",s),e.idScanFrontImage&&console.log("🖼️ Front Image Details:",this.getImageDataInfo(e.idScanFrontImage)),e.idScanBackImage&&console.log("🖼️ Back Image Details:",this.getImageDataInfo(e.idScanBackImage)),e.idScan&&console.log("🆔 ID Scan Data:",this.getTruncatedData(e.idScan));const a=JSON.stringify(e);console.log(`📏 Total Request Size: ${a.length} characters`),a.length>1e3?(console.log("📄 Request Body (truncated):"),console.log(this.smartTruncate(a,1e3))):console.log("📄 Request Body:",a)}logFrontScanResponse(e,t){const s=performance.now()-t;console.log("📥 FRONT SCAN RESPONSE:"),console.log(`⏱️ Duration: ${s.toFixed(2)}ms`),console.log("✅ Response Structure:",{wasProcessed:e.wasProcessed,error:e.error,hasScanResultBlob:!!e.scanResultBlob,hasOriginalResponse:!!e.originalResponse,hasErrorMessage:!!e.errorMessage}),e.scanResultBlob&&console.log(`📦 Scan Result Blob Length: ${e.scanResultBlob.length} characters`),e.originalResponse&&console.log("📋 Original Response Keys:",Object.keys(e.originalResponse))}logBackScanResponse(e,t){const s=performance.now()-t;if(console.log("📥 BACK SCAN RESPONSE:"),console.log(`⏱️ Duration: ${s.toFixed(2)}ms`),console.log("✅ Response Structure:",{wasProcessed:e.wasProcessed,error:e.error,hasScanResultBlob:!!e.scanResultBlob,hasOriginalResponse:!!e.originalResponse,hasErrorMessage:!!e.errorMessage,hasOcrData:!!e.originalResponse?.data?.documentData}),e.scanResultBlob&&console.log(`📦 Scan Result Blob Length: ${e.scanResultBlob.length} characters`),e.originalResponse?.data?.documentData){console.log("📄 OCR Document Data Present: Yes");try{const t=JSON.parse(e.originalResponse.data.documentData);console.log("📊 OCR Data Structure:",{hasScannedValues:!!t.scannedValues,groupCount:t.scannedValues?.groups?.length||0})}catch(e){console.error("❌ Failed to parse OCR document data:",e.message)}}}maskSensitiveData(e){return e&&"string"==typeof e?e.length<=8?"*".repeat(e.length):e.substring(0,4)+"*".repeat(e.length-8)+e.substring(e.length-4):String(e)}getImageDataInfo(e){if(!e||"string"!=typeof e)return{type:"invalid",length:0};const t={length:e.length,type:"unknown"};if(e.startsWith("data:image/")){const s=e.match(/data:image\/([^;]+)/);s&&(t.type=s[1])}else e.startsWith("/9j/")?t.type="jpeg (base64)":e.startsWith("iVBORw0KGgo")?t.type="png (base64)":t.type="base64 data";return t}getTruncatedData(e){if("string"==typeof e)return this.smartTruncate(e,200);if("object"==typeof e&&null!==e){const t=JSON.stringify(e);return t.length>200?this.smartTruncate(t,200):e}return e}smartTruncate(e,t){if(!e||e.length<=t)return e;const s=Math.floor(t/2)-10,a=e.substring(0,s),o=e.substring(e.length-s);return`${a}...[${e.length-t} chars truncated]...${o}`}}},266:(e,t,s)=>{const a=s(307),o=s(719);e.exports=function(e,t,s,n){var r=this;this.latestNetworkRequest=new XMLHttpRequest,this.deviceKey=s||null,this.additionalHeaders=n||{},this.postLivenessCheckUseCase=new a,this.processSessionResultWhileFaceTecSDKWaits=function(e,t){if(r.latestSessionResult=e,e.status!==FaceTecSDK.FaceTecSessionStatus.SessionCompletedSuccessfully)return r.latestNetworkRequest.abort(),r.latestNetworkRequest=new XMLHttpRequest,void t.cancel();r.executeLivenessCheckUseCase(e,t)},this.executeLivenessCheckUseCase=async function(e,t){try{const s=function(e){t.uploadProgress(e)},a=await r.postLivenessCheckUseCase.execute({sessionResult:e,deviceKey:r.deviceKey,additionalHeaders:r.additionalHeaders,onProgress:s});a.success?(FaceTecSDK.FaceTecCustomization.setOverrideResultScreenSuccessMessage("Face Scanned\n3D Liveness Proven"),t.proceedToNextStep(a.scanResultBlob)):r.cancelDueToNetworkError(a.errorMessage||"Unexpected API response, cancelling out.",t)}catch(e){console.error("LivenessCheckProcessor - executeLivenessCheckUseCase error:",e),r.cancelDueToNetworkError(e.message||"Exception while handling API response, cancelling out.",t)}},this.onFaceTecSDKCompletelyDone=function(){null!==r.latestSessionResult&&(r.success=r.latestSessionResult.isCompletelyDone),r.success&&o.logMessage("Liveness Check Complete"),r.sampleAppControllerReference.onComplete(r.latestSessionResult,200)},this.cancelDueToNetworkError=function(e,t){!1===r.cancelledDueToNetworkError&&(console.error(e),r.cancelledDueToNetworkError=!0,t.cancel())},this.isSuccess=function(){return r.success},this.success=!1,this.sampleAppControllerReference=t,this.latestSessionResult=null,this.cancelledDueToNetworkError=!1,new FaceTecSDK.FaceTecSession(this,e)}},307:(e,t,s)=>{const a=s(216);e.exports=class{constructor(){this.faceTecRepository=new a}async execute(e){try{const{sessionResult:t,deviceKey:a,additionalHeaders:o,onProgress:n}=e;console.log("=== POST LIVENESS CHECK USE CASE - STARTING ==="),console.log("Input params:",{hasSessionResult:!!t,deviceKey:a,additionalHeaders:o,hasOnProgress:"function"==typeof n}),t&&console.log("Session result details:",{sessionId:t.sessionId,hasFaceScan:!!t.faceScan,hasAuditTrail:!!t.auditTrail&&t.auditTrail.length>0,hasLowQualityAuditTrail:!!t.lowQualityAuditTrail&&t.lowQualityAuditTrail.length>0,auditTrailLength:t.auditTrail?t.auditTrail.length:0,lowQualityAuditTrailLength:t.lowQualityAuditTrail?t.lowQualityAuditTrail.length:0});const r={...o};if(a&&(r["X-Device-Key"]=a),t.sessionId&&"undefined"!=typeof FaceTecSDK&&(r["X-User-Agent"]=FaceTecSDK.createFaceTecAPIUserAgentString(t.sessionId)),r["X-Tid"])console.log("✅ X-Tid header already present:",r["X-Tid"]);else{const e=s(599);r["X-Tid"]=e.getUniqueId(),console.log("🔧 Generated X-Tid header:",r["X-Tid"])}console.log("Prepared headers:",JSON.stringify(r,null,2));const i={faceScan:t.faceScan,auditTrailImage:t.auditTrail[0],lowQualityAuditTrailImage:t.lowQualityAuditTrail[0],sessionId:t.sessionId,function:"liveness"};console.log("=== POST LIVENESS CHECK USE CASE - PREPARED DATA ==="),console.log("Liveness data keys:",Object.keys(i)),console.log("Liveness data structure:",{function:i.function,sessionId:i.sessionId,hasFaceScan:!!i.faceScan,hasAuditTrailImage:!!i.auditTrailImage,hasLowQualityAuditTrailImage:!!i.lowQualityAuditTrailImage,faceScanLength:i.faceScan?i.faceScan.length:0,auditTrailImageLength:i.auditTrailImage?i.auditTrailImage.length:0,lowQualityAuditTrailImageLength:i.lowQualityAuditTrailImage?i.lowQualityAuditTrailImage.length:0}),console.log("Calling repository.submitLivenessCheck...");const c=await this.faceTecRepository.submitLivenessCheck(i,r,n);if(console.log("=== POST LIVENESS CHECK USE CASE - REPOSITORY RESPONSE ==="),console.log("Repository response:",JSON.stringify(c,null,2)),!0===c.wasProcessed&&!1===c.error){const e={success:!0,scanResultBlob:c.scanResultBlob};return console.log("✅ USE CASE SUCCESS - Returning:",e),e}{const e={success:!1,errorMessage:c.errorMessage||"Server returned an error."};return console.log("❌ USE CASE ERROR - Returning:",e),e}}catch(e){throw console.error("❌ PostLivenessCheckUseCase error:",e),e}}}},345:e=>{e.exports=class{static validateAll(e){if(!e)return{isValid:!1,errors:["No OCR data provided"],fieldValidations:{}};const t={},s=[],a=this.validateNationalId(e.nationalId);t.nationalId=a,a.isValid||s.push(`National ID: ${a.error}`),t.titleTh=this.validateRequiredField(e.titleTh,"Title (TH)"),t.firstNameTh=this.validateRequiredField(e.firstNameTh,"First Name (TH)"),t.middleNameTh=this.validateOptionalField(e.middleNameTh,"Middle Name (TH)"),t.lastNameTh=this.validateRequiredField(e.lastNameTh,"Last Name (TH)"),t.titleEn=this.validateRequiredField(e.titleEn,"Title (EN)"),t.firstNameEn=this.validateRequiredField(e.firstNameEn,"First Name (EN)"),t.middleNameEn=this.validateOptionalField(e.middleNameEn,"Middle Name (EN)"),t.lastNameEn=this.validateRequiredField(e.lastNameEn,"Last Name (EN)"),t.dateOfBirth=this.validateDate(e.dateOfBirth,"Date of Birth"),t.dateOfIssue=this.validateDate(e.dateOfIssue,"Date of Issue"),t.dateOfExpiry=this.validateDate(e.dateOfExpiry,"Date of Expiry"),t.laserId=this.validateLaserId(e.laserId),Object.values(t).forEach((e=>{!e.isValid&&e.error&&s.push(e.error)}));const o=this.validateIdCardExpiry(e.dateOfExpiry);return o.isValid||s.push(o.error),{isValid:0===s.length,errors:s,fieldValidations:t,isExpired:!o.isValid}}static validateNationalId(e){if(!e||"string"!=typeof e)return{isValid:!1,error:"National ID is required",status:"missing"};const t=e.replace(/[\s-]/g,"");return/^\d{13}$/.test(t)?this.validateThaiIdChecksum(t)?{isValid:!0,status:"valid"}:{isValid:!1,error:"Invalid National ID checksum",status:"invalid"}:{isValid:!1,error:"National ID must be 13 digits",status:"invalid"}}static validateThaiIdChecksum(e){if(13!==e.length)return!1;let t=0;for(let s=0;s<12;s++)t+=parseInt(e[s])*(13-s);const s=t%11;return(s<2?s:11-s)===parseInt(e[12])}static validateRequiredField(e,t){return e&&"string"==typeof e&&""!==e.trim()?{isValid:!0,status:"valid"}:{isValid:!1,error:`${t} is required`,status:"missing"}}static validateOptionalField(e,t){return e&&"string"==typeof e&&""!==e.trim()?{isValid:!0,status:"valid"}:{isValid:!0,status:"optional"}}static validateDate(e,t){if(!e||"string"!=typeof e)return{isValid:!1,error:`${t} is required`,status:"missing"};const s=this.parseDate(e);return!s||isNaN(s.getTime())?{isValid:!1,error:`${t} has invalid date format`,status:"invalid"}:{isValid:!0,status:"valid",parsedDate:s}}static parseDate(e){if(!e)return null;const t=[/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,/^(\d{1,2})-(\d{1,2})-(\d{4})$/,/^(\d{4})-(\d{1,2})-(\d{1,2})$/,/^(\d{1,2})\.(\d{1,2})\.(\d{4})$/];for(const s of t){const t=e.match(s);if(t){let e,a,o;s.source.startsWith("^(\\d{4})")?(o=parseInt(t[1]),a=parseInt(t[2])-1,e=parseInt(t[3])):(e=parseInt(t[1]),a=parseInt(t[2])-1,o=parseInt(t[3]));const n=new Date(o,a,e);if(n.getFullYear()===o&&n.getMonth()===a&&n.getDate()===e)return n}}return null}static validateLaserId(e){return e&&"string"==typeof e&&""!==e.trim()?/^[A-Za-z0-9]+$/.test(e.trim())?{isValid:!0,status:"valid"}:{isValid:!1,error:"Laser ID contains invalid characters",status:"invalid"}:{isValid:!1,error:"Laser ID is required",status:"missing"}}static validateIdCardExpiry(e){const t=this.validateDate(e,"Expiry Date");if(!t.isValid)return t;const s=t.parsedDate,a=new Date;return a.setHours(0,0,0,0),s<a?{isValid:!1,error:"ID card has expired",status:"expired"}:{isValid:!0,status:"valid"}}static getStatusIcon(e){switch(e){case"valid":return"✓";case"invalid":return"✗";case"missing":return"⚠";case"optional":return"○";case"expired":return"⏰";default:return"?"}}static getStatusColor(e){switch(e){case"valid":return"#4CAF50";case"invalid":return"#F44336";case"missing":return"#FF9800";case"optional":return"#9E9E9E";case"expired":return"#FF5722";default:return"#757575"}}}},347:(e,t,s)=>{const a=s(592),o=s(863),n=s(995),r=s(985),i=s(161),c=s(548),{TokenStorage:l}=s(411),{UuidGenerator:d}=s(411),u=s(382),g=new i(new c),p=new a,f=new o,h=new n(g),m=new r(g),S=s(955),D=s(453),y=async(e={},t=!0)=>{try{e["X-Session-Id"]&&l.storeSessionId(e["X-Session-Id"]);const s=await h.execute(e),a=s.toJSON();if(t){const e=s.getEkycToken();e&&l.storeEkycToken(e)}return a}catch(e){throw console.error("Error getting session token:",e),e}},k=async(e={},t=!0)=>{try{const s=l.getSessionId();s&&!e["X-Session-Id"]&&(e={...e,"X-Session-Id":s});const a=(await m.execute(e)).toJSON();if(a.faceTecInitialized=!1,t&&a&&"CUS-KYC-1000"===a.code&&a.data&&a.data.deviceKey&&a.data.encryptionKey)try{await u.initializeFaceTec(a.data.deviceKey,a.data.encryptionKey),console.log("FaceTec SDK initialized successfully"),a.faceTecInitialized=!0}catch(e){console.error("Error initializing FaceTec SDK:",e),a.faceTecError=e.message||"Failed to initialize FaceTec SDK"}return a}catch(e){throw console.error("Error getting FaceTec session token with eKYC token:",e),e}},I=async(e={},t=null,a=null)=>{try{if(!t)throw new Error("deviceKey parameter is required for Photo ID Scan");if(!a.faceTecInitialized)throw new Error("FaceTec SDK not initialized properly");const o=await u.loadFaceTecSDK();o.setResourceDirectory("/core-sdk/FaceTecSDK.js/resources"),o.setImagesDirectory("/core-sdk/FaceTec_images");const n={onComplete:(e,t,s)=>({sessionResult:e,idScanResult:t,networkResponseStatus:s})},r={"X-Session-Id":e["X-Session-Id"]||a.data?.sessionId,"X-Ekyc-Token":a.data?.ekycToken||l.getEkycToken(),correlationid:e.correlationid||s(411).UuidGenerator.getUniqueId()},i=new S(a.data.sessionFaceTec,n,t,r);return new Promise(((e,t)=>{n.onComplete=(s,a,o)=>{i.isSuccess()?e({sessionResult:s,idScanResult:a,networkResponseStatus:o}):t(new Error("ID scan failed"))}}))}catch(e){throw console.error("Error performing photo ID scan:",e),e}};e.exports={formatCurrency:(e,t="USD")=>p.execute(e,t),greet:e=>f.execute(e),getSessionToken:y,getStoredEkycToken:()=>l.getEkycToken(),clearEkycToken:()=>l.removeEkycToken(),getFaceTecSessionTokenWithEkycToken:k,performPhotoIDScan:I,initEkyc:async(e={})=>{const{sessionId:t,token:a,environment:o="development",language:n="en",initCallback:r}=e;if(!t)throw new Error("sessionId is required for eKYC initialization");if(!a)throw new Error("token is required for eKYC initialization");try{console.log("🚀 Initializing eKYC SDK with sessionId:",t);const{UuidGenerator:e}=s(411);let i=l.getToken("ekyc_device_id");i||(i=e.getUniqueId(),l.storeToken("ekyc_device_id",i)),l.storeToken("ekyc_session_id",t),l.storeToken("ekyc_api_token",a),l.storeToken("ekyc_environment",o),l.storeToken("ekyc_language",n);const c={Authorization:`Bearer ${a}`,"X-Session-Id":t,"X-Ekyc-Device-Info":`browser|${i}|${"undefined"!=typeof window?window.location.origin:"unknown"}|${n}|${n.toUpperCase()}`};console.log("📡 Getting session token...");const d=await y(c,!0);console.log("🎭 Getting FaceTec session token and initializing...");const u=await k(c,!0),g={success:!0,sessionToken:d,faceTecToken:u,environment:o,language:n,sessionId:t,initialized:!0,faceTecInitialized:u.faceTecInitialized||!1};return console.log("✅ eKYC SDK initialized successfully"),r&&"function"==typeof r&&r(g),g}catch(e){console.error("❌ Error initializing eKYC SDK:",e);const s={success:!1,error:e.message||"Failed to initialize eKYC SDK",environment:o,language:n,sessionId:t,initialized:!1};throw r&&"function"==typeof r&&r(s),e}},ocrIdCard:async(e={})=>{const{checkExpiredIdCard:t=!0,checkDopa:s=!1,enableConfirmInfo:a=!0,callback:o}=e;try{console.log("📄 Starting OCR ID Card scan...");const e=l.getToken("ekyc_session_id"),t=l.getToken("ekyc_device_id")||"unknown";if(!e)throw new Error("eKYC SDK not initialized. Call initEkyc() first.");const s={"X-Session-Id":e,"X-Ekyc-Device-Info":`browser|${t}`,"X-Ekyc-Token":l.getToken("ekyc_token")};console.log("🎭 Ensuring FaceTec SDK is initialized...");const a=await k(s,!0);if(!a.faceTecInitialized)throw new Error("FaceTec SDK not properly initialized");console.log("🔍 Performing ID scan...");const n=await I(s,t,a,ocrResultsCallback),r=n.processor?n.processor.getCallbackResult():null,i={success:!r||r.success,description:r?r.description:"OCR ID Card scan completed successfully",userOcrValue:r?r.userOcrValue:null,userConfirmedValue:r?r.userConfirmedValue:null,dopaResult:r?r.dopaResult:null};return console.log("✅ OCR ID Card scan completed"),console.log("📊 Standardized result structure:",{success:i.success,description:i.description,hasUserOcrValue:!!i.userOcrValue,hasUserConfirmedValue:!!i.userConfirmedValue}),o&&"function"==typeof o&&o(i),i}catch(e){console.error("❌ Error performing OCR ID card scan:",e);const t={success:!1,description:e.message||"Unable to Process",userOcrValue:null,userConfirmedValue:null,dopaResult:null};throw o&&"function"==typeof o&&o(t),e}},ocrIdCardVerifyByFace:async(e={})=>{const{checkExpiredIdCard:t=!0,checkDopa:a=!1,enableConfirmInfo:o=!0,callback:n,ocrResultsCallback:r}=e;try{console.log("📄 Starting OCR ID Card with facial verification scan...");const e=l.getToken("ekyc_session_id"),t=l.getToken("ekyc_device_id")||"unknown";if(!e)throw new Error("eKYC SDK not initialized. Call initEkyc() first.");const a={"X-Session-Id":e,"X-Ekyc-Device-Info":`browser|${t}`,"X-Ekyc-Token":l.getToken("ekyc_token")};console.log("🎭 Ensuring FaceTec SDK is initialized...");const o=await k(a,!0);if(!o.faceTecInitialized)throw new Error("FaceTec SDK not properly initialized");console.log("🔍 Performing ID scan with face verification...");const i=await(async(e={},t=null,a=null,o=null)=>{try{if(!t)throw new Error("deviceKey parameter is required for Photo ID Scan");if(!a.faceTecInitialized)throw new Error("FaceTec SDK not initialized properly");const n=await u.loadFaceTecSDK();n.setResourceDirectory("/core-sdk/FaceTecSDK.js/resources"),n.setImagesDirectory("/core-sdk/FaceTec_images");const r={onComplete:(e,t,s)=>({sessionResult:e,idScanResult:t,networkResponseStatus:s})},i={"X-Session-Id":e["X-Session-Id"]||a.data?.sessionId,"X-Ekyc-Token":a.data?.ekycToken||l.getEkycToken(),correlationid:e.correlationid||s(411).UuidGenerator.getUniqueId()},c=new D(a.data.sessionFaceTec,r,t,i,o);return new Promise(((e,t)=>{let s=!1;c._overlayShownAndCancelled=()=>{s=!0},r.onComplete=(a,o,n)=>{c.isSuccess()?e({sessionResult:a,idScanResult:o,networkResponseStatus:n,processor:c}):s?e({sessionResult:a,idScanResult:o,networkResponseStatus:n,cancelled:!0,processor:c}):t(new Error("ID scan failed"))}}))}catch(e){throw console.error("Error performing photo ID scan:",e),e}})(a,t,o,r);console.log("🔍 DEBUG: Extracting callback result from scanResult"),console.log("📊 DEBUG: scanResult structure:",{hasProcessor:!!i.processor,scanResultKeys:Object.keys(i),processorType:i.processor?i.processor.constructor.name:"N/A"});const c=i.processor?i.processor.getCallbackResult():null;console.log("📋 DEBUG: Retrieved callback result:",{hasCallbackResult:!!c,callbackResultKeys:c?Object.keys(c):[],callbackResult:c}),console.log("🔍 DEBUG: Raw scan result analysis:",{sessionResult:i.sessionResult?"present":"missing",idScanResult:i.idScanResult?"present":"missing",networkResponseStatus:i.networkResponseStatus,scanResultData:i});let d=null;c&&c.userOcrValue?(console.log("✅ DEBUG: Using userOcrValue from callback result"),d=c.userOcrValue):i.processor&&i.processor.extractOcrFromApiResponse&&i.idScanResult?(console.log("🔍 DEBUG: Extracting userOcrValue from scan result using processor"),d=i.processor.extractOcrFromApiResponse(i.idScanResult)):i.idScanResult&&i.idScanResult.userOcrValue&&(console.log("✅ DEBUG: Using userOcrValue directly from scan result"),d=i.idScanResult.userOcrValue),console.log("📋 DEBUG: Final extracted userOcrValue:",{hasValue:!!d,valueKeys:d?Object.keys(d):[],value:d});const g={success:!c||c.success,description:c?c.description:"OCR ID Card with face verification completed successfully",userOcrValue:d,userConfirmedValue:c?c.userConfirmedValue:null,dopaResult:c?c.dopaResult:null};return console.log("✅ OCR ID Card with face verification completed"),console.log("📊 Standardized result structure:",{success:g.success,description:g.description,hasUserOcrValue:!!g.userOcrValue,hasUserConfirmedValue:!!g.userConfirmedValue}),n&&"function"==typeof n&&n(g),g}catch(e){console.error("❌ Error performing OCR ID card with face verification:",e);const t={success:!1,description:e.message||"Failed to perform OCR ID card with face verification",userOcrValue:null,userConfirmedValue:null,dopaResult:null};throw n&&"function"==typeof n&&n(t),e}},ndidVerification:async(e={})=>{const{identifierType:t,identifierValue:s,serviceId:a,ndidVerificationCallback:o}=e;if(!t)throw new Error("identifierType is required for NDID verification");if(!s)throw new Error("identifierValue is required for NDID verification");if(!a)throw new Error("serviceId is required for NDID verification");try{console.log("🆔 Starting NDID verification...");const e=l.getToken("ekyc_session_id");if(l.getToken("ekyc_device_id"),!e)throw new Error("eKYC SDK not initialized. Call initEkyc() first.");const n=`ndid_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;await new Promise((e=>setTimeout(e,2e3)));const r={success:!0,ndidVerified:!0,identifierType:t,identifierValue:s,serviceId:a,sessionId:e,verificationId:n,timestamp:(new Date).toISOString(),ndidResponse:{status:"verified",confidence:.95,details:{identityConfirmed:!0,documentValid:!0,biometricMatch:!0}}};return console.log("✅ NDID verification completed successfully"),o&&"function"==typeof o&&o(r),r}catch(e){console.error("❌ Error performing NDID verification:",e);const n={success:!1,error:e.message||"Failed to perform NDID verification",identifierType:t,identifierValue:s,serviceId:a};throw o&&"function"==typeof o&&o(n),e}},livenessCheck:async(e={})=>{const{livenessCheckCallback:t}=e;try{console.log("👁️ Starting liveness check...");const e=l.getToken("ekyc_session_id"),a=l.getToken("ekyc_device_id")||"unknown";if(!e)throw new Error("eKYC SDK not initialized. Call initEkyc() first.");const o={"X-Session-Id":e,"X-Ekyc-Device-Info":`browser|${a}`};console.log("🎭 Ensuring FaceTec SDK is initialized...");const n=await k(o,!0);if(!n.faceTecInitialized)throw new Error("FaceTec SDK not properly initialized");const r={"X-Device-Key":a||n.data?.deviceKey,"X-Session-Id":e,"X-Ekyc-Token":n.data?.ekycToken||l.getEkycToken(),"X-Tid":d.getUniqueId(),correlationid:d.getUniqueId()},i={onComplete:(e,t)=>({sessionResult:e,networkResponseStatus:t})},c=new Promise(((e,t)=>{i.onComplete=(s,a)=>{u.isSuccess()||s&&s.status===FaceTecSDK.FaceTecSessionStatus.SessionCompletedSuccessfully?e({sessionResult:s,networkResponseStatus:a}):t(new Error("Liveness check failed or was cancelled"))}}));console.log("🔍 Creating LivenessCheckProcessor...");const u=new(s(266))(n.data.sessionFaceTec,i,r["X-Device-Key"],r);console.log("⏳ Waiting for liveness check to complete...");const g=await c,p={success:u.isSuccess(),liveness:{sessionId:g.sessionResult?.sessionId||`liveness_${Date.now()}`,livenessScore:1,isLive:u.isSuccess(),confidence:u.isSuccess()?"high":"low",timestamp:(new Date).toISOString()},sessionId:e,deviceId:a,faceTecInitialized:!0,rawResult:g};return console.log("✅ Liveness check completed successfully:",p.success),t&&"function"==typeof t&&t({responseCode:p.success?"CUS-KYC-1000":"ERROR",description:p.success?"Liveness check successful":"Unable to Process",success:p.success,data:p}),p}catch(e){throw console.error("❌ Error performing liveness check:",e),e.message,t&&"function"==typeof t&&t({responseCode:"ERROR",responseDescription:e.message||"Failed to perform liveness check",success:!1,error:e.message}),e}}}},382:(e,t,s)=>{const a=new(s(706));e.exports={loadFaceTecSDK:()=>a.loadFaceTecSDK(),initializeFaceTec:(e,t)=>a.initializeFaceTec(e,t),getFaceTecVersion:()=>a.getFaceTecVersion()}},405:(e,t,s)=>{const a=s(719);e.exports=class{constructor(){this.baseUrl="/api"}async postIDScanFront(e,t={},s=null){const o=performance.now();return new Promise(((n,r)=>{const i=new XMLHttpRequest,c=`${this.baseUrl}/match-3d-2d-idscan/front`;a.logMessage(`Starting front ID scan request to: ${c}`),s&&"function"==typeof s&&i.upload.addEventListener("progress",(e=>{if(e.lengthComputable){const t=e.loaded/e.total*100;s(t),a.logData("Front scan upload progress",`${t.toFixed(1)}%`)}})),i.addEventListener("loadend",(()=>{const e=performance.now();if(i.readyState===XMLHttpRequest.DONE)try{if(i.status>=200&&i.status<300){const t=JSON.parse(i.responseText);a.logPerformance("IDScanFrontDataSource.postIDScanFront",o,e),a.logApiCall(c,"POST",`Success (${i.status})`),a.logData("Front API Response",{status:i.status,wasProcessed:t.wasProcessed,error:t.error,hasScanResultBlob:!!t.scanResultBlob,scanType:"front"}),n(t)}else{a.logPerformance("IDScanFrontDataSource.postIDScanFront (failed)",o,e),a.logError(`Front API call failed with status ${i.status}`);let t=null;try{t=JSON.parse(i.responseText)}catch(e){a.logError("Failed to parse error response",e)}r(new Error(`HTTP error! status: ${i.status}, message: ${t?.errorMessage||"Unknown error"}`))}}catch(t){a.logPerformance("IDScanFrontDataSource.postIDScanFront (parse error)",o,e),a.logError("Error parsing front scan response:",t),r(t)}})),i.addEventListener("error",(()=>{const e=performance.now();a.logPerformance("IDScanFrontDataSource.postIDScanFront (network error)",o,e),a.logError("Network error during front ID scan request"),r(new Error("Network error occurred during front ID scan"))})),i.addEventListener("timeout",(()=>{const e=performance.now();a.logPerformance("IDScanFrontDataSource.postIDScanFront (timeout)",o,e),a.logError("Front ID scan request timed out"),r(new Error("Front ID scan request timed out"))})),i.open("POST",c,!0),i.timeout=6e4,i.setRequestHeader("Content-Type","application/json"),i.setRequestHeader("Accept","application/json"),Object.keys(t).forEach((e=>{void 0!==t[e]&&null!==t[e]&&(i.setRequestHeader(e,t[e]),a.logData(`Front scan header: ${e}`,t[e]))}));const l=JSON.stringify(e);a.logData("Front scan request body size",`${l.length} bytes`),a.logData("Front scan data keys",Object.keys(e)),i.send(l)}))}validateFrontScanData(e){if(!e)throw new Error("Front scan data is required");if(!e.idScan)throw new Error("ID scan data is required for front scan");if(!e.idScanFrontImage)throw new Error("Front image is required for front ID scan");return a.logMessage("Front scan data validation passed"),!0}}},411:(e,t,s)=>{const a=s(599),o=s(641);e.exports={UuidGenerator:a,TokenStorage:o}},453:(e,t,s)=>{const a=s(0),o=s(535),n=s(715),r=s(307),i=s(719),c=s(345);var l=l=function(e,t,s,l,d){var u=this;this.latestNetworkRequest=new XMLHttpRequest,this.deviceKey=s||null,this.additionalHeaders=l||{},this.ocrResultsCallback=d||null,this.postIDScanOnlyUseCase=new a,this.postIDScanFrontUseCase=new o,this.postIDScanBackUseCase=new n,this.postLivenessCheckUseCase=new r,this.processSessionResultWhileFaceTecSDKWaits=function(e,t){if(u.latestSessionResult=e,e.status!==FaceTecSDK.FaceTecSessionStatus.SessionCompletedSuccessfully)return u.latestNetworkRequest.abort(),u.latestNetworkRequest=new XMLHttpRequest,void t.cancel();u.executeLivenessCheckUseCase(e,t)},this.executeLivenessCheckUseCase=async function(e,t){try{const s=function(e){t.uploadProgress(e)},a=await u.postLivenessCheckUseCase.execute({sessionResult:e,deviceKey:u.deviceKey,additionalHeaders:u.additionalHeaders,onProgress:s});a.success?(FaceTecSDK.FaceTecCustomization.setOverrideResultScreenSuccessMessage("Face Scanned\n3D Liveness Proven"),t.proceedToNextStep(a.scanResultBlob)):u.cancelDueToNetworkError(a.errorMessage||"Liveness check failed",t)}catch(e){console.error("Liveness check use case error:",e),u.cancelDueToNetworkError("Network error during liveness check",t)}},this.processIDScanResultWhileFaceTecSDKWaits=function(e,t){if(u.latestIDScanResult=e,e.status!==FaceTecSDK.FaceTecIDScanStatus.Success)return u.latestNetworkRequest.abort(),u.latestNetworkRequest=new XMLHttpRequest,void t.cancel();u.executeIDScanUseCase(e,t)},this.executeIDScanUseCase=async function(e,t){try{const s=function(e){t.uploadProgress(e)};let a,o="unknown";if(e.backImages&&e.backImages[0]?(o="back",i.logMessage("Routing to back ID scan endpoint"),a=await u.postIDScanBackUseCase.execute({idScanResult:e,deviceKey:u.deviceKey,additionalHeaders:u.additionalHeaders,onProgress:s})):e.frontImages&&e.frontImages[0]&&(o="front",i.logMessage("Routing to front ID scan endpoint"),a=await u.postIDScanFrontUseCase.execute({idScanResult:e,deviceKey:u.deviceKey,additionalHeaders:u.additionalHeaders,onProgress:s})),i.logData("ID scan completed",{scanType:o,success:a.success}),a.success)if(a.allowRetry)i.logMessage(`${o} scan: Retry allowed - ${a.retryReason}`),u.configureRetryMessages(o),t.proceedToNextStep(a.scanResultBlob);else{if(u.ocrResultsCallback){console.log("🔍 DEBUG: Starting OCR extraction for callback"),console.log("📊 DEBUG: Input result for OCR extraction:",{resultKeys:Object.keys(a),hasUserOcrValue:!!a.userOcrValue,hasOcrData:!!a.ocrData,hasOriginalResponse:!!a.originalResponse,result:a});let e=null;a.userOcrValue?(console.log("✅ DEBUG: Using userOcrValue from Use Case (already processed)"),e=a.userOcrValue):(console.log("🔍 DEBUG: Extracting OCR from raw API response"),e=u.extractOcrFromApiResponse(a)),console.log("📋 DEBUG: OCR extraction completed:",{hasExtractedValue:!!e,extractedValueKeys:e?Object.keys(e):[],extractedValue:e}),u.callbackResult={success:!0,description:"OCR ID Card with face verification completed successfully",userOcrValue:e,userConfirmedValue:null,dopaResult:null},console.log("✅ DEBUG: Stored callback result for success case:",u.callbackResult),i.logMessage("Stored callback result for success case"),i.logData("Success Callback Result",u.callbackResult)}"back"===o&&a.userOcrValue&&u.showSuccessOverlay(a.userOcrValue),u.configureSuccessMessages(o),t.proceedToNextStep(a.scanResultBlob)}else{if(u.ocrResultsCallback&&!a.allowRetry){let e=null;a.userOcrValue?(console.log("✅ DEBUG: Using userOcrValue from Use Case for error case (already processed)"),e=a.userOcrValue):(console.log("🔍 DEBUG: Extracting OCR from raw API response for error case"),e=u.extractOcrFromApiResponse(a)),u.callbackResult={success:!1,description:a.errorMessage||"OCR ID Card with face verification failed",userOcrValue:e,userConfirmedValue:null,dopaResult:null},i.logMessage("Stored callback result for error case"),i.logData("Error Callback Result",u.callbackResult)}u.cancelDueToNetworkError(a.errorMessage||`${o} ID scan failed`,t)}}catch(e){console.error("ID scan use case error:",e),u.ocrResultsCallback&&(u.callbackResult={success:!1,description:"Network error during OCR ID Card with face verification: "+e.message,userOcrValue:null,userConfirmedValue:null,dopaResult:null},i.logMessage("Stored callback result for exception case"),i.logData("Exception Callback Result",u.callbackResult)),u.cancelDueToNetworkError("Network error during ID scan",t)}},this.configureSuccessMessages=function(e){"front"===e?FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides("Front Scan Complete","Front of ID<br/>Scanned","Front Scan Complete","Passport Scan Complete","Photo ID Front<br/>Complete","ID Photo Capture<br/>Complete","Face Didn't Match<br/>Highly Enough","ID Document<br/>Not Fully Visible","ID Text Not Legible","ID Type Mismatch<br/>Please Try Again"):"back"===e?FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides("Back Scan Complete","Back of ID<br/>Scanned","ID Scan Complete","Passport Scan Complete","Photo ID Back<br/>Complete","ID Photo Capture<br/>Complete","Face Didn't Match<br/>Highly Enough","ID Document<br/>Not Fully Visible","ID Text Not Legible","ID Type Mismatch<br/>Please Try Again"):FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides("Front Scan Complete","Front of ID<br/>Scanned","ID Scan Complete","Passport Scan Complete","Photo ID Scan<br/>Complete","ID Photo Capture<br/>Complete","Face Didn't Match<br/>Highly Enough","ID Document<br/>Not Fully Visible","ID Text Not Legible","ID Type Mismatch<br/>Please Try Again")},this.configureRetryMessages=function(e){i.logMessage(`Configuring retry messages for ${e} scan (CUS-KYC-7102)`),"front"===e?FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides("Front Scan Complete<br/>Please Try Again","Front of ID Scanned<br/>Please Try Again","Front Scan Complete<br/>Please Try Again","Passport Scan Complete<br/>Please Try Again","Photo ID Front Complete<br/>Please Try Again","ID Photo Capture Complete<br/>Please Try Again","Face Didn't Match<br/>Highly Enough","ID Document<br/>Not Fully Visible","ID Text Not Legible","ID Type Mismatch<br/>Please Try Again"):"back"===e?FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides("Back Scan Complete<br/>Please Try Again","Back of ID Scanned<br/>Please Try Again","ID Scan Complete<br/>Please Try Again","Passport Scan Complete<br/>Please Try Again","Photo ID Back Complete<br/>Please Try Again","ID Photo Capture Complete<br/>Please Try Again","Face Didn't Match<br/>Highly Enough","ID Document<br/>Not Fully Visible","ID Text Not Legible","ID Type Mismatch<br/>Please Try Again"):FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides("Scan Complete<br/>Please Try Again","ID Scanned<br/>Please Try Again","ID Scan Complete<br/>Please Try Again","Passport Scan Complete<br/>Please Try Again","Photo ID Scan Complete<br/>Please Try Again","ID Photo Capture Complete<br/>Please Try Again","Face Didn't Match<br/>Highly Enough","ID Document<br/>Not Fully Visible","ID Text Not Legible","ID Type Mismatch<br/>Please Try Again")},this.showSuccessOverlay=function(e=null){let t=null;e&&(t=c.validateAll(e),i.logData("OCR Validation Result",t));const s=document.createElement("div");s.id="ekyc-success-overlay",s.style.cssText="\n                position: fixed;\n                top: 0;\n                left: 0;\n                width: 100%;\n                height: 100%;\n                background: rgba(0, 0, 0, 0.9);\n                display: flex;\n                justify-content: center;\n                align-items: center;\n                z-index: 10000;\n                font-family: Arial, sans-serif;\n                overflow-y: auto;\n                padding: 20px;\n                box-sizing: border-box;\n            ";const a=e?u.generateOcrFieldsHtml(e,t):"";s.innerHTML=`\n                <div style="\n                    background: white;\n                    border-radius: 12px;\n                    max-width: 600px;\n                    width: 100%;\n                    max-height: 90vh;\n                    overflow-y: auto;\n                    box-shadow: 0 8px 32px rgba(0,0,0,0.3);\n                ">\n                    \x3c!-- Header --\x3e\n                    <div style="\n                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n                        color: white;\n                        padding: 24px;\n                        border-radius: 12px 12px 0 0;\n                        text-align: center;\n                        position: relative;\n                    ">\n                        <h2 style="margin: 0; font-size: 24px; font-weight: 600;">Review & Confirm</h2>\n                        <p style="margin: 8px 0 0 0; opacity: 0.9; font-size: 14px;">Information</p>\n                        <button onclick="document.getElementById('ekyc-success-overlay').remove()"\n                                style="\n                                    position: absolute;\n                                    top: 16px;\n                                    right: 16px;\n                                    background: rgba(255,255,255,0.2);\n                                    border: none;\n                                    color: white;\n                                    width: 32px;\n                                    height: 32px;\n                                    border-radius: 50%;\n                                    cursor: pointer;\n                                    font-size: 18px;\n                                    display: flex;\n                                    align-items: center;\n                                    justify-content: center;\n                                ">×</button>\n                    </div>\n\n                    \x3c!-- Content --\x3e\n                    <div style="padding: 24px;">\n                        ${a||'<div style="text-align: center; color: #666; padding: 40px;"><div style="font-size: 48px; margin-bottom: 16px;">✓</div><h3 style="margin: 0 0 8px 0;">ID Card Scan Complete</h3><p style="margin: 0; opacity: 0.7;">Back side of ID card successfully scanned</p></div>'}\n\n                        \x3c!-- Action Buttons --\x3e\n                        <div style="margin-top: 32px; text-align: center;">\n                            <button id="ekyc-confirm-button"\n                                    style="\n                                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n                                        color: white;\n                                        border: none;\n                                        padding: 12px 32px;\n                                        border-radius: 8px;\n                                        font-size: 16px;\n                                        font-weight: 600;\n                                        cursor: pointer;\n                                        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n                                        transition: transform 0.2s;\n                                    "\n                                    onmouseover="this.style.transform='translateY(-2px)'"\n                                    onmouseout="this.style.transform='translateY(0)'">\n                                Confirm Information\n                            </button>\n                        </div>\n\n                        ${t&&t.errors.length>0?'\n                        <div style="margin-top: 16px; padding: 12px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px;">\n                            <p style="margin: 0; color: #856404; font-size: 14px; font-weight: 600;">Please check your information before submitting</p>\n                        </div>\n                        ':""}\n                    </div>\n                </div>\n            `,document.body.appendChild(s);const o=document.getElementById("ekyc-confirm-button");o&&e&&o.addEventListener("click",(function(){u.handleUserConfirmation(e)})),u._overlayShownAndCancelled&&u._overlayShownAndCancelled(),faceTecIdScanResultCallback.cancel(),setTimeout((()=>{document.getElementById("ekyc-success-overlay")&&document.getElementById("ekyc-success-overlay").remove()}),3e4)},this.generateOcrFieldsHtml=function(e,t){let s="";return[{key:"nationalId",label:"National ID Number",required:!0},{key:"titleTh",label:"Title (TH)",required:!0},{key:"firstNameTh",label:"First Name (TH)",required:!0},{key:"middleNameTh",label:"Middle Name (TH)",required:!1,note:"(if applicable)"},{key:"lastNameTh",label:"Last Name (TH)",required:!0},{key:"titleEn",label:"Title (ENG)",required:!0},{key:"firstNameEn",label:"First Name (ENG)",required:!0},{key:"middleNameEn",label:"Middle Name (ENG)",required:!1,note:"(if applicable)"},{key:"lastNameEn",label:"Last Name (ENG)",required:!0},{key:"dateOfBirth",label:"Birth Date",required:!0,note:"(A.D. i.e. 2022)",isDate:!0},{key:"dateOfIssue",label:"Issue Date",required:!0,note:"(A.D. i.e. 2022)",isDate:!0},{key:"dateOfExpiry",label:"Expiry Date",required:!0,note:"(A.D. i.e. 2022)",isDate:!0},{key:"laserId",label:"Laser ID",required:!0}].forEach((a=>{const o=e[a.key]||"",n=t?.fieldValidations[a.key],r=n?.status||"unknown",i=c.getStatusIcon(r),l=c.getStatusColor(r);let d=o,u=null;a.isDate&&o&&(u=o.split(/[\/\-\.]/),3===u.length&&(d=`${u[0]} / ${u[1]} / ${u[2]}`)),s+=`\n                    <div style="margin-bottom: 16px;">\n                        <label style="\n                            display: block;\n                            font-size: 14px;\n                            font-weight: 600;\n                            color: #333;\n                            margin-bottom: 6px;\n                        ">\n                            ${a.label}${a.required?"*":""}\n                            ${a.note?`<span style="font-weight: 400; color: #666; font-size: 12px;"> ${a.note}</span>`:""}\n                            <span style="color: ${l}; margin-left: 8px; font-size: 16px;">${i}</span>\n                        </label>\n                        ${a.isDate?`\n                            <div style="display: flex; gap: 8px; align-items: center;">\n                                <input type="text" value="${u&&u[0]?u[0]:""}" placeholder="DD"\n                                       style="width: 60px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; text-align: center;" readonly />\n                                <span style="color: #666;">/</span>\n                                <input type="text" value="${u&&u[1]?u[1]:""}" placeholder="MM"\n                                       style="width: 60px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; text-align: center;" readonly />\n                                <span style="color: #666;">/</span>\n                                <input type="text" value="${u&&u[2]?u[2]:""}" placeholder="YYYY"\n                                       style="width: 80px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; text-align: center;" readonly />\n                            </div>\n                        `:`\n                            <input type="text" value="${d}"\n                                   style="\n                                       width: 100%;\n                                       padding: 12px;\n                                       border: 1px solid ${"invalid"===r||"missing"===r?"#f44336":"#ddd"};\n                                       border-radius: 6px;\n                                       font-size: 14px;\n                                       background: ${"missing"===r?"#fff3e0":"#f9f9f9"};\n                                       color: #333;\n                                   " readonly />\n                        `}\n                        ${n&&!n.isValid&&n.error?`\n                            <div style="margin-top: 4px; color: #f44336; font-size: 12px;">\n                                ${n.error}\n                            </div>\n                        `:""}\n                    </div>\n                `})),s},this.handleUserConfirmation=function(e){if(i.logMessage("User confirmed information from overlay"),i.logData("User Confirmed OCR Value",e),u.ocrResultsCallback){let t=null;t=u.callbackResult&&u.callbackResult.userOcrValue?u.userOcrValue:e,u.callbackResult={success:!0,description:"OCR ID Card with face verification completed successfully",userOcrValue:t,userConfirmedValue:e,dopaResult:null},i.logMessage("Stored callback result for user confirmation"),i.logData("User Confirmation Callback Result",u.callbackResult)}const t=document.getElementById("ekyc-success-overlay");t&&t.remove()},this.getCallbackResult=function(){return u.callbackResult||null},this.executeStoredCallback=function(){return!(!u.ocrResultsCallback||!u.callbackResult||(i.logMessage("Executing stored OCR results callback"),i.logData("Callback Result",u.callbackResult),u.ocrResultsCallback(u.callbackResult.success,u.callbackResult.description,u.callbackResult.userOcrValue,u.callbackResult.userConfirmedValue,u.callbackResult.dopaResult),0))},this.extractOcrValueForCallback=function(e){return console.log("🔍 DEBUG: extractOcrValueForCallback called - delegating to extractOcrFromApiResponse"),console.log("📊 DEBUG: Input result structure:",{hasResult:!!e,resultType:typeof e,resultKeys:e?Object.keys(e):[],hasUserOcrValue:e&&!!e.userOcrValue,hasOcrData:e&&!!e.ocrData,hasOriginalResponse:e&&!!e.originalResponse,userOcrValueContent:e&&e.userOcrValue,result:e}),i.logMessage("Extracting OCR value for callback - delegating to extractOcrFromApiResponse"),i.logData("Input result for OCR extraction",e),u.extractOcrFromApiResponse(e)},this.extractOcrFromApiResponse=function(e){try{if(console.log("🔍 DEBUG: extractOcrFromApiResponse called"),console.log("📊 DEBUG: API Data structure:",{hasApiData:!!e,apiDataType:typeof e,apiDataKeys:e?Object.keys(e):[],hasDocumentData:e&&!!e.documentData,hasUserOcrValue:e&&!!e.userOcrValue,hasOriginalResponse:e&&!!e.originalResponse,documentDataType:e&&e.documentData?typeof e.documentData:"N/A",apiData:e}),i.logMessage("Extracting OCR data from API response"),i.logData("API Data",e),e&&e.userOcrValue)return console.log("✅ DEBUG: Using existing userOcrValue from apiData (from Use Case)"),console.log("📊 DEBUG: userOcrValue content:",e.userOcrValue),i.logMessage("Using existing userOcrValue from apiData"),i.logData("UserOcrValue from Use Case",e.userOcrValue),e.userOcrValue;e&&e.originalResponse&&e.originalResponse.data&&e.originalResponse.data.documentData&&(console.log("🔍 DEBUG: Extracting OCR data from originalResponse structure"),i.logMessage("Extracting OCR data from originalResponse structure"),e=e.originalResponse.data);const t={nationalId:null,titleTh:null,firstNameTh:null,middleNameTh:null,lastNameTh:null,titleEn:null,firstNameEn:null,middleNameEn:null,lastNameEn:null,dateOfBirth:null,dateOfIssue:null,dateOfExpiry:null,laserId:null};if(e.documentData){let s;console.log("✅ DEBUG: Found documentData, processing..."),s="string"==typeof e.documentData?JSON.parse(e.documentData):e.documentData,s.scannedValues&&s.scannedValues.groups&&s.scannedValues.groups.forEach((e=>{e.fields&&e.fields.forEach((e=>{const s=e.fieldKey,a=e.value||"";switch(s){case"nationalId":case"idNumber":t.nationalId=a;break;case"fullname":const e=a.split(" ").filter((e=>e.trim()));e.length>=2&&(t.titleTh=e[0],t.firstNameTh=e[1],e.length>2&&(t.lastNameTh=e.slice(2).join(" ")));break;case"firstName":const s=a.split(" ").filter((e=>e.trim()));s.length>=1&&(t.titleEn=s[0],s.length>1&&(t.firstNameEn=s.slice(1).join(" ")));break;case"lastName":case"lastNameEn":t.lastNameEn=a;break;case"dateOfBirth":t.dateOfBirth=u.convertThaiDateToStandard(a);break;case"dateOfIssue":t.dateOfIssue=u.convertThaiDateToStandard(a);break;case"dateOfExpiry":t.dateOfExpiry=u.convertThaiDateToStandard(a);break;case"laserId":t.laserId=a}}))}))}return i.logMessage("OCR extraction completed"),i.logData("Extracted OCR Value",t),t}catch(e){return i.logMessage("Error extracting OCR data: "+e.message),console.error("Error extracting OCR data:",e),null}},this.convertThaiDateToStandard=function(e){if(!e)return null;try{const t={"ม.ค.":"01","ก.พ.":"02","มี.ค.":"03","เม.ย.":"04","พ.ค.":"05","มิ.ย.":"06","ก.ค.":"07","ส.ค.":"08","ก.ย.":"09","ต.ค.":"10","พ.ย.":"11","ธ.ค.":"12"},s=e.match(/(\d{1,2})\s+([ก-ฮ\.]+)\s+(\d{4})/);if(s){const e=s[1].padStart(2,"0"),a=s[2],o=s[3],n=t[a];if(n)return`${e}/${n}/${o}`}return e}catch(t){return console.error("Error converting Thai date:",t),e}},this.onFaceTecSDKCompletelyDone=function(){null!=u.latestIDScanResult&&(u.success=u.latestIDScanResult.isCompletelyDone),u.sampleAppControllerReference.onComplete(u.latestSessionResult,u.latestIDScanResult,u.latestNetworkRequest.status)},this.cancelDueToNetworkError=function(e,t){!1===u.cancelledDueToNetworkError&&(console.error(e),u.cancelledDueToNetworkError=!0,t.cancel())},this.isSuccess=function(){return u.success},this.success=!1,this.sampleAppControllerReference=t,this.latestSessionResult=null,this.latestIDScanResult=null,this.cancelledDueToNetworkError=!1,FaceTecSDK.FaceTecCustomization.setIDScanUploadMessageOverrides("Uploading<br/>Encrypted<br/>ID Scan","Still Uploading...<br/>Slow Connection","Upload Complete","Processing<br/>ID Scan","Uploading<br/>Encrypted<br/>Back of ID","Still Uploading...<br/>Slow Connection","Upload Complete","Processing<br/>Back of ID","Uploading<br/>Your Confirmed Info","Still Uploading...<br/>Slow Connection","Info Saved","Processing"),new FaceTecSDK.FaceTecSession(this,e)};e.exports=l},489:(e,t,s)=>{const a=s(719);e.exports=class{constructor(){this.baseUrl="/api"}async postIDScanBack(e,t={},s=null){const o=performance.now();return new Promise(((n,r)=>{const i=new XMLHttpRequest,c=`${this.baseUrl}/match-3d-2d-idscan/back`;a.logMessage(`Starting back ID scan request to: ${c}`),s&&"function"==typeof s&&i.upload.addEventListener("progress",(e=>{if(e.lengthComputable){const t=e.loaded/e.total*100;s(t),a.logData("Back scan upload progress",`${t.toFixed(1)}%`)}})),i.addEventListener("loadend",(()=>{const e=performance.now();if(i.readyState===XMLHttpRequest.DONE)try{if(i.status>=200&&i.status<300){const t=JSON.parse(i.responseText);a.logPerformance("IDScanBackDataSource.postIDScanBack",o,e),a.logApiCall(c,"POST",`Success (${i.status})`),a.logData("Back API Response",{status:i.status,wasProcessed:t.wasProcessed,error:t.error,hasScanResultBlob:!!t.scanResultBlob,hasOcrData:!!t.originalResponse?.data?.documentData,scanType:"back"}),n(t)}else{a.logPerformance("IDScanBackDataSource.postIDScanBack (failed)",o,e),a.logError(`Back API call failed with status ${i.status}`);let t=null;try{t=JSON.parse(i.responseText)}catch(e){a.logError("Failed to parse error response",e)}r(new Error(`HTTP error! status: ${i.status}, message: ${t?.errorMessage||"Unknown error"}`))}}catch(t){a.logPerformance("IDScanBackDataSource.postIDScanBack (parse error)",o,e),a.logError("Error parsing back scan response:",t),r(t)}})),i.addEventListener("error",(()=>{const e=performance.now();a.logPerformance("IDScanBackDataSource.postIDScanBack (network error)",o,e),a.logError("Network error during back ID scan request"),r(new Error("Network error occurred during back ID scan"))})),i.addEventListener("timeout",(()=>{const e=performance.now();a.logPerformance("IDScanBackDataSource.postIDScanBack (timeout)",o,e),a.logError("Back ID scan request timed out"),r(new Error("Back ID scan request timed out"))})),i.open("POST",c,!0),i.timeout=6e4,i.setRequestHeader("Content-Type","application/json"),i.setRequestHeader("Accept","application/json"),Object.keys(t).forEach((e=>{void 0!==t[e]&&null!==t[e]&&(i.setRequestHeader(e,t[e]),a.logData(`Back scan header: ${e}`,t[e]))}));const l=JSON.stringify(e);a.logData("Back scan request body size",`${l.length} bytes`),a.logData("Back scan data keys",Object.keys(e)),i.send(l)}))}validateBackScanData(e){if(!e)throw new Error("Back scan data is required");if(!e.idScan)throw new Error("ID scan data is required for back scan");if(!e.idScanBackImage)throw new Error("Back image is required for back ID scan");return a.logMessage("Back scan data validation passed"),!0}}},518:(e,t,s)=>{const a=s(719);e.exports=class{constructor(){this.baseUrl="/api"}async postIDScanOnly(e,t={},s=null){return new Promise(((o,n)=>{const r=performance.now();try{const i=`${this.baseUrl}/idscan-only`;a.logApiCall(i,"POST","Starting request");const c=new XMLHttpRequest;s&&"function"==typeof s&&(c.upload.onprogress=function(e){if(e.lengthComputable){const t=e.loaded/e.total;a.logIDScanProgress("Uploading",t),s(t)}}),c.onreadystatechange=function(){if(c.readyState===XMLHttpRequest.DONE){const e=performance.now();try{if(c.status>=200&&c.status<300){const t=JSON.parse(c.responseText);a.logPerformance("IDScanDataSource.postIDScanOnly",r,e),a.logApiCall(i,"POST",`Success (${c.status})`),a.logData("API Response",{status:c.status,wasProcessed:t.wasProcessed,error:t.error,hasScanResultBlob:!!t.scanResultBlob}),o(t)}else a.logPerformance("IDScanDataSource.postIDScanOnly (failed)",r,e),a.logError(`API call failed with status ${c.status}`),n(new Error(`HTTP error! status: ${c.status}`))}catch(e){a.logError("Failed to parse API response",e),n(new Error("Failed to parse response JSON"))}}},c.onerror=function(){const e=performance.now();a.logPerformance("IDScanDataSource.postIDScanOnly (network error)",r,e),a.logError("Network request failed"),n(new Error("Network request failed"))},c.open("POST",i),c.setRequestHeader("Content-Type","application/json"),Object.keys(t).forEach((e=>{void 0!==t[e]&&c.setRequestHeader(e,t[e])}));const l=JSON.stringify(e);a.logMessage(`Sending request to ${i} with ${Object.keys(e).length} data fields`),c.send(l)}catch(e){a.logError("IDScanDataSource - postIDScanOnly error",e),n(e)}}))}}},535:(e,t,s)=>{const a=s(216),o=s(599),n=s(719);e.exports=class{constructor(){this.faceTecRepository=new a}async execute({idScanResult:e,deviceKey:t,additionalHeaders:s={},onProgress:a=null}){const o=performance.now();try{n.logMessage("Starting PostIDScanFrontUseCase execution"),n.logMessage("Preparing front scan data...");const r=this.prepareFrontScanData(e);n.logData("Front Scan Data Keys",Object.keys(r)),n.logMessage("Preparing headers...");const i=this.prepareHeaders(e,t,s);n.logData("Request Headers",Object.keys(i)),n.logMessage("Validating front scan data..."),this.faceTecRepository.validateFrontScanData(r),n.logSuccess("Front scan data validation passed"),n.logMessage("Submitting front scan to repository...");const c=await this.faceTecRepository.submitIDScanFront(r,i,a);n.logMessage("Processing front scan response...");const l=this.processResponse(c),d=performance.now();return n.logPerformance("PostIDScanFrontUseCase.execute",o,d),n.logSuccess(`Front UseCase completed successfully: ${l.success}`),l}catch(e){const t=performance.now();throw n.logPerformance("PostIDScanFrontUseCase.execute (failed)",o,t),n.logError("PostIDScanFrontUseCase execution failed:",e),e}}prepareFrontScanData(e){const t={idScan:e.idScan,enableConfirmInfo:!0};if(!e.frontImages||!e.frontImages[0])throw new Error("Front image is required for front ID scan processing");return t.idScanFrontImage=e.frontImages[0],t}prepareHeaders(e,t,s){const a={...s};return t&&(a["X-Device-Key"]=t),e.sessionId&&"undefined"!=typeof FaceTecSDK&&(a["X-User-Agent"]=FaceTecSDK.createFaceTecAPIUserAgentString(e.sessionId)),a["X-Tid"]?n.logData("X-Tid header already present for front scan",a["X-Tid"]):(a["X-Tid"]=o.getUniqueId(),n.logData("Generated X-Tid header for front scan",a["X-Tid"])),a["Content-Type"]="application/json",a.Accept="application/json",Object.keys(a).forEach((e=>{void 0===a[e]&&delete a[e]})),n.logData("Front scan prepared headers",Object.keys(a)),a}processResponse(e){const t=this.shouldAllowRetryForResponse(e);let s=null;if(e.originalResponse&&e.originalResponse.data&&e.originalResponse.data.documentData)try{const t=JSON.parse(e.originalResponse.data.documentData);n.logData("Parsed front scan documentData",t),s=this.extractFrontOcrDataFromDocumentData(t),s?n.logData("Extracted Front OCR Data",Object.keys(s)):n.logMessage("No valid front OCR fields found in documentData")}catch(e){n.logError("Failed to parse front scan documentData JSON:",e),n.logMessage("No front OCR data found in response")}else n.logMessage("No documentData found in front scan response");return{success:!0===e.wasProcessed&&!1===e.error||t,scanResultBlob:e.scanResultBlob||e.originalResponse?.data?.scanResultBlob,originalResponse:e.originalResponse,errorMessage:e.errorMessage,userOcrValue:s,scanType:"front",allowRetry:t,retryReason:t?"CUS-KYC-7102: OCR Match Photo ID and Selfie Fail - Retry Allowed":null}}shouldAllowRetryForResponse(e){return!(!e.originalResponse||"CUS-KYC-7102"!==e.originalResponse.code||(n.logMessage("Front scan: CUS-KYC-7102 detected - allowing retry with scanResultBlob"),e.scanResultBlob||e.originalResponse?.data?.scanResultBlob?(n.logData("Front scan: ScanResultBlob available for retry","Yes"),0):(n.logError("Front scan: CUS-KYC-7102 detected but no scanResultBlob available for retry"),1)))}extractFrontOcrDataFromDocumentData(e){if(!e||!e.scannedValues||!e.scannedValues.groups)return n.logMessage("Invalid front scan documentData structure"),null;const t={nationalId:null,scanType:"front",scanQuality:null},s={idNumber:"nationalId",quality:"scanQuality"};return e.scannedValues.groups.forEach((e=>{e.fields&&Array.isArray(e.fields)&&e.fields.forEach((e=>{if(e.fieldKey&&e.value&&s[e.fieldKey]){const a=s[e.fieldKey];t[a]=e.value,n.logData(`Front scan mapped ${e.fieldKey} -> ${a}`,e.value)}}))})),Object.values(t).some((e=>null!==e))?t:(n.logMessage("No valid front OCR fields found in documentData structure"),null)}}},548:(e,t,s)=>{const a=s(599),{TokenStorage:o}=s(411);e.exports=class{async getSessionToken(e={}){try{const t="/api/session-token",s=e["X-Ekyc-Device-Info"]?null:a.getDeviceId(),n=a.getUniqueId(),r=e["X-Session-Id"]||a.getUniqueId();e["X-Session-Id"]||o.storeSessionId(r);const i={"Content-Type":"application/json",Accept:"application/json","X-Ekyc-Sdk-Version":"1.0.0","X-Ekyc-Device-Info":`browser|${s}|https://ekyc-internal-ekyc-browser-demo-dev.ekyc.np.aella.tech/|en|EN`,"X-Session-Id":`${r}`,"X-Tid":`${n}`,correlationid:`${a.getUniqueId()}`,...e};e.Authorization&&(i.Authorization=e.Authorization);const c=await fetch(t,{method:"GET",headers:i});if(!c.ok)throw new Error(`API request failed with status ${c.status}`);return await c.json()}catch(e){throw console.error("Error getting session token:",e),e}}async getFaceTecSessionToken(e={}){try{const t="/api/facetec-session-token",s=e["X-Ekyc-Device-Info"]?null:a.getDeviceId(),n=a.getUniqueId(),r=o.getSessionId(),i={"Content-Type":"application/json",Accept:"application/json","X-Ekyc-Sdk-Version":"1.0.0","X-Ekyc-Device-Info":`browser|${s}|https://ekyc-internal-ekyc-browser-demo-dev.ekyc.np.aella.tech/|en|EN`,"X-Session-Id":`${e["X-Session-Id"]||r||a.getUniqueId()}`,"X-Tid":`${n}`,correlationid:`${a.getUniqueId()}`,...e};e.Authorization&&(i.Authorization=e.Authorization);const c=await fetch(t,{method:"GET",headers:i});if(!c.ok)throw new Error(`API request failed with status ${c.status}`);return await c.json()}catch(e){throw console.error("Error getting FaceTec session token:",e),e}}async getFaceTecSessionTokenWithEkycToken(e={}){try{const t="/api/facetec-session-token",s=o.getEkycToken();if(!s)throw new Error("No eKYC token found. Please get a session token first.");const n=e["X-Ekyc-Device-Info"]?null:a.getDeviceId(),r=e["X-Tid"]||a.getUniqueId(),i=o.getSessionId(),c=e["X-Session-Id"]||i||a.getUniqueId(),l=e.correlationid||a.getUniqueId(),d={"Content-Type":"application/json",Accept:"application/json","X-Ekyc-Sdk-Version":e["X-Ekyc-Sdk-Version"]||"1.0.0","X-Ekyc-Device-Info":e["X-Ekyc-Device-Info"]||`browser|${n}`,"X-Session-Id":`${c}`,"X-Tid":`${r}`,correlationid:`${l}`,"X-Ekyc-Token":s,...e};e.Authorization&&(d.Authorization=e.Authorization);const u=await fetch(t,{method:"GET",headers:d});if(!u.ok)throw new Error(`API request failed with status ${u.status}`);return await u.json()}catch(e){throw console.error("Error getting FaceTec session token with eKYC token:",e),e}}}},592:(e,t,s)=>{const a=s(189);e.exports=class{execute(e,t="USD"){return new a(e,t).format()}}},599:e=>{e.exports=class{static generateUuid(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){const t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}static getDeviceId(){if("undefined"!=typeof window&&window.localStorage){let e=localStorage.getItem("ekyc_device_id");return e||(e=this.generateUuid(),localStorage.setItem("ekyc_device_id",e)),e}return this.generateUuid()}static getUniqueId(){return this.generateUuid()}}},641:e=>{e.exports=class{static storeToken(e,t){if("undefined"!=typeof window&&window.localStorage&&t)try{return localStorage.setItem(e,t),!0}catch(e){return console.error("Error storing token:",e),!1}return!1}static getToken(e){return"undefined"!=typeof window&&window.localStorage?localStorage.getItem(e):null}static removeToken(e){if("undefined"!=typeof window&&window.localStorage)try{return localStorage.removeItem(e),!0}catch(e){return console.error("Error removing token:",e),!1}return!1}static storeEkycToken(e){return this.storeToken("ekyc_token",e)}static getEkycToken(){return this.getToken("ekyc_token")}static removeEkycToken(){return this.removeToken("ekyc_token")}static storeSessionId(e){return this.storeToken("ekyc_session_id",e)}static getSessionId(){return this.getToken("ekyc_session_id")}static removeSessionId(){return this.removeToken("ekyc_session_id")}}},706:e=>{e.exports=class{loadFaceTecSDK(){return new Promise(((e,t)=>{if("undefined"!=typeof window&&window.FaceTecSDK)return void e(window.FaceTecSDK);const s=document.createElement("script");s.src="/core-sdk/FaceTecSDK.js/FaceTecSDK.js",s.async=!0,s.onload=()=>{window.FaceTecSDK?e(window.FaceTecSDK):t(new Error("FaceTecSDK not found after loading script"))},s.onerror=()=>{t(new Error("Failed to load FaceTecSDK script"))},document.head.appendChild(s)}))}async initializeFaceTec(e,t){try{const s=await this.loadFaceTecSDK();return s.setResourceDirectory("/core-sdk/FaceTecSDK.js/resources"),s.setImagesDirectory("/core-sdk/FaceTec_images"),new Promise(((a,o)=>{s.initializeInDevelopmentMode(e,t,(e=>{e?(console.log("FaceTecSDK initialized successfully"),a(!0)):(console.error("FaceTecSDK failed to initialize"),o(new Error("FaceTecSDK failed to initialize")))}))}))}catch(e){throw console.error("Error loading FaceTecSDK:",e),e}}async getFaceTecVersion(){try{return(await this.loadFaceTecSDK()).version()}catch(e){throw console.error("Error getting FaceTecSDK version:",e),e}}}},715:(e,t,s)=>{const a=s(216),o=s(599),n=s(719);e.exports=class{constructor(){this.faceTecRepository=new a}async execute({idScanResult:e,deviceKey:t,additionalHeaders:s={},onProgress:a=null}){const o=performance.now();try{n.logMessage("Starting PostIDScanBackUseCase execution"),n.logMessage("Preparing back scan data...");const r=this.prepareBackScanData(e);n.logData("Back Scan Data Keys",Object.keys(r)),n.logMessage("Preparing headers...");const i=this.prepareHeaders(e,t,s);n.logData("Request Headers",Object.keys(i)),n.logMessage("Validating back scan data..."),this.faceTecRepository.validateBackScanData(r),n.logSuccess("Back scan data validation passed"),n.logMessage("Submitting back scan to repository...");const c=await this.faceTecRepository.submitIDScanBack(r,i,a);n.logMessage("Processing back scan response...");const l=this.processResponse(c),d=performance.now();return n.logPerformance("PostIDScanBackUseCase.execute",o,d),n.logSuccess(`Back UseCase completed successfully: ${l.success}`),l}catch(e){const t=performance.now();throw n.logPerformance("PostIDScanBackUseCase.execute (failed)",o,t),n.logError("PostIDScanBackUseCase execution failed:",e),e}}prepareBackScanData(e){const t={idScan:e.idScan,enableConfirmInfo:!0};if(!e.backImages||!e.backImages[0])throw new Error("Back image is required for back ID scan processing");return t.idScanBackImage=e.backImages[0],e.frontImages&&e.frontImages[0]&&(t.idScanFrontImage=e.frontImages[0]),t}prepareHeaders(e,t,s){const a={...s};return t&&(a["X-Device-Key"]=t),e.sessionId&&"undefined"!=typeof FaceTecSDK&&(a["X-User-Agent"]=FaceTecSDK.createFaceTecAPIUserAgentString(e.sessionId)),a["X-Tid"]?n.logData("X-Tid header already present for back scan",a["X-Tid"]):(a["X-Tid"]=o.getUniqueId(),n.logData("Generated X-Tid header for back scan",a["X-Tid"])),a["Content-Type"]="application/json",a.Accept="application/json",Object.keys(a).forEach((e=>{void 0===a[e]&&delete a[e]})),n.logData("Back scan prepared headers",Object.keys(a)),a}processResponse(e){const t=this.shouldAllowRetryForResponse(e);let s=null;if(e.originalResponse&&e.originalResponse.data&&e.originalResponse.data.documentData)try{const t=JSON.parse(e.originalResponse.data.documentData);n.logData("Parsed back scan documentData",t),s=this.extractBackOcrDataFromDocumentData(t),s?n.logData("Extracted Back OCR Data",Object.keys(s)):n.logMessage("No valid back OCR fields found in documentData")}catch(e){n.logError("Failed to parse back scan documentData JSON:",e),n.logMessage("No back OCR data found in response")}else n.logMessage("No documentData found in back scan response");return{success:!0===e.wasProcessed&&!1===e.error||t,scanResultBlob:e.scanResultBlob||e.originalResponse?.data?.scanResultBlob,originalResponse:e.originalResponse,errorMessage:e.errorMessage,userOcrValue:s,scanType:"back",allowRetry:t,retryReason:t?"CUS-KYC-7102: OCR Match Photo ID and Selfie Fail - Retry Allowed":null}}shouldAllowRetryForResponse(e){return!(!e.originalResponse||"CUS-KYC-7102"!==e.originalResponse.code||(n.logMessage("Back scan: CUS-KYC-7102 detected - allowing retry with scanResultBlob"),e.scanResultBlob||e.originalResponse?.data?.scanResultBlob?(n.logData("Back scan: ScanResultBlob available for retry","Yes"),0):(n.logError("Back scan: CUS-KYC-7102 detected but no scanResultBlob available for retry"),1)))}extractBackOcrDataFromDocumentData(e){if(!e||!e.scannedValues||!e.scannedValues.groups)return n.logMessage("Invalid back scan documentData structure"),null;const t={nationalId:null,titleTh:null,firstNameTh:null,middleNameTh:null,lastNameTh:null,titleEn:null,firstNameEn:null,middleNameEn:null,lastNameEn:null,dateOfBirth:null,dateOfIssue:null,dateOfExpiry:null,laserId:null},s={idNumber:"nationalId",firstName:"_rawFirstName",fullname:"_rawFullname",fullName:"_rawFullname",lastName:"lastNameEn",dateOfBirth:"dateOfBirth",dateOfIssue:"dateOfIssue",dateOfExpiration:"dateOfExpiry",customField1:"laserId"},a={_rawFirstName:null,_rawFullname:null};return e.scannedValues.groups.forEach((e=>{e.fields&&Array.isArray(e.fields)&&e.fields.forEach((e=>{if(e.fieldKey&&e.value&&s[e.fieldKey]){const o=s[e.fieldKey];o.startsWith("_raw")?(a[o]=e.value,n.logData(`Stored raw ${e.fieldKey}`,e.value)):(t[o]=e.value,n.logData(`Mapped ${e.fieldKey} -> ${o}`,e.value))}}))})),a._rawFullname&&this.parseThaiNameFields(a._rawFullname,t),a._rawFirstName&&this.parseEnglishNameFields(a._rawFirstName,t),this.convertDateFields(t),this.addFieldEditabilityMetadata(t),Object.values(t).some((e=>null!==e))?t:(n.logMessage("No valid back OCR fields found in documentData structure"),null)}parseThaiNameFields(e,t){if(!e||"string"!=typeof e)return void n.logMessage("Invalid fullname for Thai parsing");const s=e.trim().split(/\s+/);n.logData("Thai name parts",s),s.length>=1&&(t.titleTh=s[0],n.logData("Extracted titleTh",t.titleTh)),s.length>=2&&(t.firstNameTh=s[1],n.logData("Extracted firstNameTh",t.firstNameTh)),s.length>=3&&(t.lastNameTh=s.slice(2).join(" "),n.logData("Extracted lastNameTh",t.lastNameTh))}parseEnglishNameFields(e,t){if(!e||"string"!=typeof e)return void n.logMessage("Invalid firstName for English parsing");const s=e.trim().split(/\s+/);n.logData("English name parts",s),s.length>=1&&(t.titleEn=s[0],n.logData("Extracted titleEn",t.titleEn)),s.length>=2&&(t.firstNameEn=s.slice(1).join(" "),n.logData("Extracted firstNameEn",t.firstNameEn))}convertDateFields(e){["dateOfBirth","dateOfIssue","dateOfExpiry"].forEach((t=>{if(e[t]){const s=this.convertThaiDateFormat(e[t]);s?(n.logData(`Converted ${t}`,`${e[t]} -> ${s}`),e[t]=s):n.logError(`Failed to convert date field ${t}`,e[t])}}))}convertThaiDateFormat(e){if(!e||"string"!=typeof e)return null;try{const t={"ม.ค.":"01",มกราคม:"01","ม.ค":"01","ก.พ.":"02",กุมภาพันธ์:"02","ก.พ":"02","มี.ค.":"03",มีนาคม:"03","มี.ค":"03","เม.ย.":"04",เมษายน:"04","เม.ย":"04","พ.ค.":"05",พฤษภาคม:"05","พ.ค":"05","มิ.ย.":"06",มิถุนายน:"06","มิ.ย":"06","ก.ค.":"07",กรกฎาคม:"07","ก.ค":"07","ส.ค.":"08",สิงหาคม:"08","ส.ค":"08","ก.ย.":"09",กันยายน:"09","ก.ย":"09","ต.ค.":"10",ตุลาคม:"10","ต.ค":"10","พ.ย.":"11",พฤศจิกายน:"11","พ.ย":"11","ธ.ค.":"12",ธันวาคม:"12","ธ.ค":"12"},s=e.trim();if(/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(s))return n.logData("Date already in standard format",s),s;const a=s.split(/\s+/);if(a.length>=3){const s=a[0].padStart(2,"0"),o=a[1],r=a[2],i=t[o];if(i&&/^\d{4}$/.test(r)){const t=`${s}/${i}/${r}`;return n.logData("Thai date conversion successful",`${e} -> ${t}`),t}}const o=s.split("/");if(3===o.length){const s=o[0].padStart(2,"0"),a=o[1],r=o[2],i=t[a];if(i&&/^\d{4}$/.test(r)){const t=`${s}/${i}/${r}`;return n.logData("Thai date conversion (slash format) successful",`${e} -> ${t}`),t}}return n.logError("Unable to parse Thai date format",e),null}catch(e){return n.logError("Error converting Thai date format",e),null}}addFieldEditabilityMetadata(e){const t=["titleTh","firstNameTh","lastNameTh","titleEn","firstNameEn","lastNameEn","dateOfBirth","dateOfIssue","dateOfExpiry"],s=["nationalId","laserId"];e._fieldMetadata={editableFields:t,readOnlyFields:s,dateFields:["dateOfBirth","dateOfIssue","dateOfExpiry"],requiredFields:["nationalId","titleTh","firstNameTh","lastNameTh","dateOfBirth","dateOfExpiry"],optionalFields:["middleNameTh","middleNameEn","titleEn","firstNameEn","lastNameEn","dateOfIssue","laserId"]},Object.keys(e).forEach((a=>{"_fieldMetadata"!==a&&null!==e[a]&&(e[`_${a}Properties`]={isEditable:t.includes(a),isReadOnly:s.includes(a),isDate:["dateOfBirth","dateOfIssue","dateOfExpiry"].includes(a),isRequired:["nationalId","titleTh","firstNameTh","lastNameTh","dateOfBirth","dateOfExpiry"].includes(a),originalValue:e[a],hasBeenModified:!1})})),n.logData("Added field editability metadata",{editableCount:t.length,readOnlyCount:s.length,dateFieldCount:3})}}},719:e=>{e.exports=class{static logMessage(e,t="info"){const s=`[${(new Date).toISOString()}] [${t.toUpperCase()}]`;switch(t){case"error":console.error(`${s} ${e}`);break;case"warn":console.warn(`${s} ${e}`);break;case"success":console.log(`%c${s} ${e}`,"color: green; font-weight: bold;");break;default:console.log(`${s} ${e}`)}}static logFaceTecStatus(e){this.logMessage(`FaceTec SDK: ${e}`,"info")}static logApiCall(e,t,s){this.logMessage(`API ${t} ${e}: ${s}`,"info")}static logSuccess(e){this.logMessage(e,"success")}static logError(e,t=null){let s=e;t&&(s+=` - ${t.message}`),this.logMessage(s,"error"),t&&t.stack&&console.error("Stack trace:",t.stack)}static logWarning(e){this.logMessage(e,"warn")}static logIDScanProgress(e,t=null){let s=`ID Scan: ${e}`;null!==t&&(s+=` (${Math.round(100*t)}%)`),this.logMessage(s,"info")}static logLivenessProgress(e,t=null){let s=`Liveness Check: ${e}`;null!==t&&(s+=` (${Math.round(100*t)}%)`),this.logMessage(s,"info")}static logSession(e,t){this.logMessage(`Session ${e}: ${t}`,"info")}static clearConsole(){"function"==typeof console.clear&&console.clear()}static logData(e,t){console.group(`📊 ${e}`),console.log(t),console.groupEnd()}static logPerformance(e,t,s){const a=s-t;this.logMessage(`Performance: ${e} took ${a.toFixed(2)}ms`,"info")}}},777:(e,t,s)=>{const a=s(719);e.exports=class{constructor(){this.baseUrl="/api"}async postLivenessCheck(e,t={},s=null){return new Promise(((o,n)=>{const r=performance.now();try{const i=`${this.baseUrl}/enrollment-3d`;console.log("=== LIVENESS CHECK DATA SOURCE - STARTING REQUEST ==="),console.log("URL:",i),console.log("Input livenessData:",JSON.stringify(e,null,2)),console.log("Input headers:",JSON.stringify(t,null,2)),console.log("Liveness data keys:",Object.keys(e||{})),console.log("Liveness data structure check:",{hasFunction:!!e?.function,hasFaceScan:!!e?.faceScan,hasAuditTrailImage:!!e?.auditTrailImage,hasLowQualityAuditTrailImage:!!e?.lowQualityAuditTrailImage,hasSessionId:!!e?.sessionId}),a.logApiCall(i,"POST","Starting request");const c=new XMLHttpRequest;s&&"function"==typeof s&&(c.upload.onprogress=function(e){if(e.lengthComputable){const t=e.loaded/e.total;a.logLivenessProgress("Uploading",t),s(t)}}),c.onreadystatechange=function(){if(c.readyState===XMLHttpRequest.DONE){const e=performance.now();try{if(console.log("=== LIVENESS CHECK DATA SOURCE - RESPONSE RECEIVED ==="),console.log("Response status:",c.status),console.log("Response text:",c.responseText),c.status>=200&&c.status<300){const t=JSON.parse(c.responseText);console.log("✅ SUCCESS - Parsed response data:",JSON.stringify(t,null,2)),a.logPerformance("LivenessCheckDataSource.postLivenessCheck",r,e),a.logApiCall(i,"POST",`Success (${c.status})`),a.logData("API Response",{status:c.status,wasProcessed:t.wasProcessed,error:t.error,hasScanResultBlob:!!t.scanResultBlob}),o(t)}else console.log("❌ ERROR - HTTP error status:",c.status),console.log("Error response text:",c.responseText),a.logPerformance("LivenessCheckDataSource.postLivenessCheck (failed)",r,e),a.logError(`API call failed with status ${c.status}`),n(new Error(`HTTP error! status: ${c.status}`))}catch(e){a.logError("Error parsing response",e),n(e)}}},c.onerror=function(){a.logError("Network error occurred"),n(new Error("Network error occurred"))},c.open("POST",i,!0),c.setRequestHeader("Content-Type","application/json"),console.log("Setting Content-Type header: application/json");for(const[e,s]of Object.entries(t))null!=s?(c.setRequestHeader(e,s),console.log(`Setting header: ${e} = ${s}`)):console.log(`Skipping header (undefined/null): ${e} = ${s}`);const l=JSON.stringify(e);console.log("=== LIVENESS CHECK DATA SOURCE - SENDING REQUEST ==="),console.log("Request body (stringified):",l),console.log("Request body size (bytes):",l.length),console.log("Request body keys from original data:",Object.keys(e)),a.logData("Request Body Keys",Object.keys(e)),console.log("📤 SENDING XMLHttpRequest..."),c.send(l)}catch(e){a.logError("Error in postLivenessCheck",e),n(e)}}))}}},863:e=>{e.exports=class{execute(e){return`Hello 12, ${e}!`}}},955:(e,t,s)=>{const a=s(0),o=s(719);e.exports=function(e,t,s,n){var r=this;this.latestNetworkRequest=new XMLHttpRequest,this.deviceKey=s||null,this.additionalHeaders=n||{},this.postIDScanOnlyUseCase=new a,this.processIDScanResultWhileFaceTecSDKWaits=function(e,t){if(r.latestIDScanResult=e,e.status!==FaceTecSDK.FaceTecIDScanStatus.Success)return r.latestNetworkRequest.abort(),r.latestNetworkRequest=new XMLHttpRequest,void t.cancel();r.executeIDScanUseCase(e,t)},this.executeIDScanUseCase=async function(e,t){try{const s=function(e){t.uploadProgress(e)},a=await r.postIDScanOnlyUseCase.execute({idScanResult:e,deviceKey:r.deviceKey,additionalHeaders:r.additionalHeaders,onProgress:s});a.success?(FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides("Front Scan Complete","Front of ID<br/>Scanned","ID Scan Complete","Passport Scan Complete","Photo ID Scan<br/>Complete","ID Photo Capture<br/>Complete","Face Didn't Match<br/>Highly Enough","ID Document<br/>Not Fully Visible","ID Text Not Legible","ID Type Mismatch<br/>Please Try Again"),t.proceedToNextStep(a.scanResultBlob)):r.cancelDueToNetworkError(a.errorMessage||"Unexpected API response, cancelling out.",t)}catch(e){console.error("PhotoIDScanProcessor - executeIDScanUseCase error:",e),r.cancelDueToNetworkError(e.message||"Exception while handling API response, cancelling out.",t)}},this.onFaceTecSDKCompletelyDone=function(){null!==r.latestIDScanResult&&(r.success=r.latestIDScanResult.isCompletelyDone),r.success&&o.logMessage("Id Scan Complete"),r.sampleAppControllerReference.onComplete(null,r.latestIDScanResult,200)},this.cancelDueToNetworkError=function(e,t){!1===r.cancelledDueToNetworkError&&(console.error(e),r.cancelledDueToNetworkError=!0,t.cancel())},this.isSuccess=function(){return r.success},this.success=!1,this.sampleAppControllerReference=t,this.latestIDScanResult=null,this.cancelledDueToNetworkError=!1,FaceTecSDK.FaceTecCustomization.setIDScanUploadMessageOverrides("Uploading<br/>Encrypted<br/>ID Scan","Still Uploading...<br/>Slow Connection","Upload Complete","Processing<br/>ID Scan","Uploading<br/>Encrypted<br/>Back of ID","Still Uploading...<br/>Slow Connection","Upload Complete","Processing<br/>Back of ID","Uploading<br/>Your Confirmed Info","Still Uploading...<br/>Slow Connection","Info Saved","Processing"),new FaceTecSDK.FaceTecSession(this,e)}},985:e=>{e.exports=class{constructor(e){this.authRepository=e}async execute(e={}){return await this.authRepository.getFaceTecSessionTokenWithEkycToken(e)}}},995:e=>{e.exports=class{constructor(e){this.authRepository=e}async execute(e={}){return await this.authRepository.getSessionToken(e)}}}},t={},function s(a){var o=t[a];if(void 0!==o)return o.exports;var n=t[a]={exports:{}};return e[a](n,n.exports,s),n.exports}(347);var e,t}));