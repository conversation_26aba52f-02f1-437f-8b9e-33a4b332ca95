const simple = require('../lib/simple');

// Mock the dependencies
jest.mock('../lib/processors/PhotoIDMatchProcessor');
jest.mock('../lib/infrastructure/utils', () => ({
  TokenStorage: {
    getToken: jest.fn(),
    storeToken: jest.fn(),
    getEkycToken: jest.fn(),
    getSessionId: jest.fn()
  },
  UuidGenerator: {
    getUniqueId: jest.fn(() => 'mock-uuid')
  }
}));

jest.mock('../lib/facetec', () => ({
  loadFaceTecSDK: jest.fn(() => Promise.resolve({
    setResourceDirectory: jest.fn(),
    setImagesDirectory: jest.fn()
  }))
}));

describe('Simple.js Callback Structure Tests', () => {
  const { TokenStorage } = require('../lib/infrastructure/utils');
  const PhotoIDMatchProcessor = require('../lib/processors/PhotoIDMatchProcessor');

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mock returns
    TokenStorage.getToken.mockImplementation((key) => {
      const tokens = {
        'ekyc_session_id': 'test-session-id',
        'ekyc_device_id': 'test-device-id',
        'ekyc_token': 'test-ekyc-token'
      };
      return tokens[key] || null;
    });
  });

  describe('ocrIdCardVerifyByFace callback structure', () => {
    it('should return standardized callback structure on success', async () => {
      // Mock successful processor result
      const mockCallbackResult = {
        success: true,
        description: "OCR ID Card with face verification completed successfully",
        userOcrValue: {
          nationalId: '1234567890123',
          titleTh: 'นาย',
          firstNameTh: 'สมชาย'
        },
        userConfirmedValue: null,
        dopaResult: null
      };

      const mockProcessor = {
        isSuccess: jest.fn(() => true),
        getCallbackResult: jest.fn(() => mockCallbackResult),
        extractOcrFromApiResponse: jest.fn(() => mockCallbackResult.userOcrValue),
        _overlayShownAndCancelled: null
      };

      PhotoIDMatchProcessor.mockImplementation(() => mockProcessor);

      // Mock getFaceTecSessionTokenWithEkycToken
      const originalGetFaceTecSessionTokenWithEkycToken = simple.getFaceTecSessionTokenWithEkycToken;
      simple.getFaceTecSessionTokenWithEkycToken = jest.fn(() => Promise.resolve({
        faceTecInitialized: true,
        data: {
          sessionFaceTec: 'mock-session',
          sessionId: 'test-session',
          ekycToken: 'test-token'
        }
      }));

      // Mock performPhotoIDMatch to return processor
      const mockScanResult = {
        sessionResult: {},
        idScanResult: {},
        networkResponseStatus: 200,
        processor: mockProcessor
      };

      // Override performPhotoIDMatch temporarily
      const originalPerformPhotoIDMatch = simple.performPhotoIDMatch;
      simple.performPhotoIDMatch = jest.fn(() => Promise.resolve(mockScanResult));

      try {
        const result = await simple.ocrIdCardVerifyByFace({
          checkExpiredIdCard: true,
          checkDopa: false,
          enableConfirmInfo: true
        });

        // Verify the result has the exact structure required
        expect(result).toEqual({
          success: true,
          description: "OCR ID Card with face verification completed successfully",
          userOcrValue: {
            nationalId: '1234567890123',
            titleTh: 'นาย',
            firstNameTh: 'สมชาย'
          },
          userConfirmedValue: null,
          dopaResult: null
        });

        // Verify all required properties exist
        expect(result).toHaveProperty('success');
        expect(result).toHaveProperty('description');
        expect(result).toHaveProperty('userOcrValue');
        expect(result).toHaveProperty('userConfirmedValue');
        expect(result).toHaveProperty('dopaResult');

        // Verify property types
        expect(typeof result.success).toBe('boolean');
        expect(typeof result.description).toBe('string');
        expect(result.userOcrValue === null || typeof result.userOcrValue === 'object').toBe(true);
        expect(result.userConfirmedValue === null || typeof result.userConfirmedValue === 'object').toBe(true);
        expect(result.dopaResult === null || typeof result.dopaResult === 'object').toBe(true);

      } finally {
        // Restore original functions
        simple.getFaceTecSessionTokenWithEkycToken = originalGetFaceTecSessionTokenWithEkycToken;
        simple.performPhotoIDMatch = originalPerformPhotoIDMatch;
      }
    });

    it('should return standardized callback structure on error', async () => {
      // Mock error case
      TokenStorage.getToken.mockReturnValue(null); // No session ID

      try {
        await simple.ocrIdCardVerifyByFace({});
        fail('Expected function to throw an error');
      } catch (error) {
        expect(error.message).toBe('eKYC SDK not initialized. Call initEkyc() first.');
      }
    });

    it('should call callback function with standardized structure', async () => {
      const mockCallbackResult = {
        success: true,
        description: "OCR ID Card with face verification completed successfully",
        userOcrValue: { nationalId: '1234567890123' },
        userConfirmedValue: null,
        dopaResult: null
      };

      const mockProcessor = {
        isSuccess: jest.fn(() => true),
        getCallbackResult: jest.fn(() => mockCallbackResult),
        extractOcrFromApiResponse: jest.fn(() => mockCallbackResult.userOcrValue),
        _overlayShownAndCancelled: null
      };

      PhotoIDMatchProcessor.mockImplementation(() => mockProcessor);

      // Mock dependencies
      simple.getFaceTecSessionTokenWithEkycToken = jest.fn(() => Promise.resolve({
        faceTecInitialized: true,
        data: { sessionFaceTec: 'mock-session', sessionId: 'test-session', ekycToken: 'test-token' }
      }));

      simple.performPhotoIDMatch = jest.fn(() => Promise.resolve({
        sessionResult: {},
        idScanResult: {},
        networkResponseStatus: 200,
        processor: mockProcessor
      }));

      const mockCallback = jest.fn();

      const result = await simple.ocrIdCardVerifyByFace({
        callback: mockCallback
      });

      // Verify callback was called with standardized structure
      expect(mockCallback).toHaveBeenCalledWith({
        success: true,
        description: "OCR ID Card with face verification completed successfully",
        userOcrValue: { nationalId: '1234567890123' },
        userConfirmedValue: null,
        dopaResult: null
      });

      // Verify return value also has standardized structure
      expect(result).toEqual({
        success: true,
        description: "OCR ID Card with face verification completed successfully",
        userOcrValue: { nationalId: '1234567890123' },
        userConfirmedValue: null,
        dopaResult: null
      });
    });
  });

  describe('Callback Structure Validation', () => {
    it('should always return the exact required structure', () => {
      const requiredStructure = {
        success: expect.any(Boolean),
        description: expect.any(String),
        userOcrValue: expect.anything(), // Can be null or object
        userConfirmedValue: expect.anything(), // Can be null or object
        dopaResult: expect.anything() // Can be null or object
      };

      // Test with mock success result
      const successResult = {
        success: true,
        description: "OCR ID Card with face verification completed successfully",
        userOcrValue: { nationalId: '1234567890123' },
        userConfirmedValue: null,
        dopaResult: null
      };

      expect(successResult).toEqual(expect.objectContaining(requiredStructure));

      // Test with mock error result
      const errorResult = {
        success: false,
        description: "OCR ID Card with face verification failed",
        userOcrValue: null,
        userConfirmedValue: null,
        dopaResult: null
      };

      expect(errorResult).toEqual(expect.objectContaining(requiredStructure));
    });
  });
});
