const PhotoIDMatchProcessor = require('../lib/processors/PhotoIDMatchProcessor');

describe('PhotoIDMatchProcessor Callback Structure Tests', () => {
  let processor;
  let mockController;
  let mockCallback;
  let callbackResults;

  beforeEach(() => {
    // Reset callback results
    callbackResults = [];
    
    // Mock callback function to capture results
    mockCallback = jest.fn((success, description, userOcrValue, userConfirmedValue, dopaResult) => {
      callbackResults.push({
        success,
        description,
        userOcrValue,
        userConfirmedValue,
        dopaResult
      });
    });

    // Mock controller
    mockController = {
      onComplete: jest.fn()
    };

    // Create processor instance with mock callback
    processor = new PhotoIDMatchProcessor(
      'mock-session-token',
      mockController,
      'mock-device-key',
      { 'X-Session-Id': 'test-session' },
      mockCallback
    );
  });

  describe('extractOcrFromApiResponse', () => {
    it('should return standardized OCR structure when given valid API data', () => {
      const mockApiData = {
        documentData: JSON.stringify({
          scannedValues: {
            groups: [{
              fields: [
                { fieldKey: 'nationalId', value: '1234567890123' },
                { fieldKey: 'fullname', value: 'นาย สมชาย ใจดี' },
                { fieldKey: 'firstName', value: 'MR. SOMCHAI JAIDEE' },
                { fieldKey: 'dateOfBirth', value: '01 ม.ค. 1990' },
                { fieldKey: 'dateOfIssue', value: '15 ก.พ. 2020' },
                { fieldKey: 'dateOfExpiry', value: '14 ก.พ. 2025' },
                { fieldKey: 'laserId', value: '*********' }
              ]
            }]
          }
        })
      };

      const result = processor.extractOcrFromApiResponse(mockApiData);

      expect(result).toEqual({
        nationalId: '1234567890123',
        titleTh: 'นาย',
        firstNameTh: 'สมชาย',
        middleNameTh: null,
        lastNameTh: 'ใจดี',
        titleEn: 'MR.',
        firstNameEn: 'SOMCHAI JAIDEE',
        middleNameEn: null,
        lastNameEn: null,
        dateOfBirth: '01/01/1990',
        dateOfIssue: '15/02/2020',
        dateOfExpiry: '14/02/2025',
        laserId: '*********'
      });
    });

    it('should return existing userOcrValue if present', () => {
      const mockApiData = {
        userOcrValue: {
          nationalId: '9876543210987',
          titleTh: 'นาง',
          firstNameTh: 'สมหญิง'
        }
      };

      const result = processor.extractOcrFromApiResponse(mockApiData);

      expect(result).toEqual(mockApiData.userOcrValue);
    });

    it('should handle null/undefined input gracefully', () => {
      expect(processor.extractOcrFromApiResponse(null)).toBeNull();
      expect(processor.extractOcrFromApiResponse(undefined)).toBeNull();
      expect(processor.extractOcrFromApiResponse({})).toEqual({
        nationalId: null,
        titleTh: null,
        firstNameTh: null,
        middleNameTh: null,
        lastNameTh: null,
        titleEn: null,
        firstNameEn: null,
        middleNameEn: null,
        lastNameEn: null,
        dateOfBirth: null,
        dateOfIssue: null,
        dateOfExpiry: null,
        laserId: null
      });
    });
  });

  describe('getCallbackResult', () => {
    it('should return null when no callback result is stored', () => {
      expect(processor.getCallbackResult()).toBeNull();
    });

    it('should return standardized callback structure for success case', () => {
      // Simulate storing a success callback result
      processor.callbackResult = {
        success: true,
        description: "OCR ID Card with face verification completed successfully",
        userOcrValue: { nationalId: '1234567890123' },
        userConfirmedValue: null,
        dopaResult: null
      };

      const result = processor.getCallbackResult();

      expect(result).toEqual({
        success: true,
        description: "OCR ID Card with face verification completed successfully",
        userOcrValue: { nationalId: '1234567890123' },
        userConfirmedValue: null,
        dopaResult: null
      });
    });

    it('should return standardized callback structure for error case', () => {
      // Simulate storing an error callback result
      processor.callbackResult = {
        success: false,
        description: "OCR ID Card with face verification failed",
        userOcrValue: null,
        userConfirmedValue: null,
        dopaResult: null
      };

      const result = processor.getCallbackResult();

      expect(result).toEqual({
        success: false,
        description: "OCR ID Card with face verification failed",
        userOcrValue: null,
        userConfirmedValue: null,
        dopaResult: null
      });
    });
  });

  describe('Callback Structure Validation', () => {
    it('should always have the required callback structure properties', () => {
      const testCases = [
        {
          success: true,
          description: "OCR ID Card with face verification completed successfully",
          userOcrValue: { nationalId: '1234567890123' },
          userConfirmedValue: null,
          dopaResult: null
        },
        {
          success: false,
          description: "OCR ID Card with face verification failed",
          userOcrValue: null,
          userConfirmedValue: null,
          dopaResult: null
        }
      ];

      testCases.forEach(testCase => {
        processor.callbackResult = testCase;
        const result = processor.getCallbackResult();

        // Verify all required properties exist
        expect(result).toHaveProperty('success');
        expect(result).toHaveProperty('description');
        expect(result).toHaveProperty('userOcrValue');
        expect(result).toHaveProperty('userConfirmedValue');
        expect(result).toHaveProperty('dopaResult');

        // Verify property types
        expect(typeof result.success).toBe('boolean');
        expect(typeof result.description).toBe('string');
        expect(result.userOcrValue === null || typeof result.userOcrValue === 'object').toBe(true);
        expect(result.userConfirmedValue === null || typeof result.userConfirmedValue === 'object').toBe(true);
        expect(result.dopaResult === null || typeof result.dopaResult === 'object').toBe(true);
      });
    });
  });
});
