import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { UuidGenerator } from 'lib/infrastructure/utils';

export default function Home() {
  // SDK state management
  const [lib, setLib] = React.useState(null);
  const [error, setError] = React.useState(null);
  const [sessionToken, setSessionToken] = useState(null);
  const [sessionLoading, setSessionLoading] = useState(false);
  const [sessionError, setSessionError] = useState(null);
  const [apiKey, setApiKey] = useState('');
  const [deviceId, setDeviceId] = useState('');
  const [sessionId, setSessionId] = useState(null);

  // eKYC SDK state
  const [sdkInitialized, setSdkInitialized] = useState(false);
  const [initializationError, setInitializationError] = useState(null);

  // Demo states for different functions
  const [activeDemo, setActiveDemo] = useState(null);
  const [demoResults, setDemoResults] = useState({});
  const [demoLoading, setDemoLoading] = useState({});

  // Load the main library
  useEffect(() => {
    // Dynamically import the library from the compiled code
    import('@lib').then(module => {
      console.log('Loaded library:', module);
      setLib(module);

      // Generate a UUID directly in the client-side code
      const generateUuid = () => {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
          const r = Math.random() * 16 | 0;
          const v = c === 'x' ? r : (r & 0x3 | 0x8);
          return v.toString(16);
        });
      };

      // Get or create a device ID from localStorage
      const getDeviceId = () => {
        if (typeof window !== 'undefined' && window.localStorage) {
          let deviceId = localStorage.getItem('ekyc_device_id');
          if (!deviceId) {
            deviceId = generateUuid();
            localStorage.setItem('ekyc_device_id', deviceId);
          }
          return deviceId;
        } else {
          return generateUuid();
        }
      };

      // Set the device ID in the state
      const generatedDeviceId = getDeviceId();
      setDeviceId(generatedDeviceId);
    }).catch(err => {
      console.error('Failed to load library:', err);
      setError('Failed to load library. Make sure to run webpack build first.');
    });
  }, []);

  // Demo function handlers
  const handleDemoFunction = async (functionName, options = {}) => {
    if (!lib) {
      setError('SDK not loaded');
      return;
    }

    // Check if the function exists
    if (typeof lib[functionName] !== 'function') {
      setError(`Function ${functionName} is not available in the SDK`);
      console.error(`Available functions:`, Object.keys(lib));
      return;
    }

    setDemoLoading(prev => ({ ...prev, [functionName]: true }));
    setDemoResults(prev => ({ ...prev, [functionName]: null }));
    setActiveDemo(functionName);

    // TODO: พี่ต้นไม้ แก้ Code ให้มันเชื่อมกับของพี่ตรงนี้นะะะ
    try {
      let result;

      switch (functionName) {
        case 'initEkyc':
          result = await lib.getSessionToken({ apiKey });
          setSdkInitialized(true);
          break;

        case 'ocrIdCard':
          result = await lib.ocrIdCard({
            checkExpiredIdCard: true,
            checkDopa: false,
            enableConfirmInfo: true,
            ...options
          });
          break;

        case 'ocrIdCardVerifyByFace':
          result = await lib.ocrIdCardVerifyByFace({
            checkExpiredIdCard: true,
            checkDopa: false,
            enableConfirmInfo: true,
            ...options
          });
          break;

        case 'ndidVerification':
          result = await lib.ndidVerification({
            identifierType: 'citizenId',
            identifierValue: '1234567890123',
            serviceId: 'demo-service',
            ...options
          });
          break;

        case 'livenessCheck':
          result = await lib.livenessCheck({
            ...options
          });
          break;

        default:
          throw new Error(`Unknown function: ${functionName}`);
      }

      setDemoResults(prev => ({ ...prev, [functionName]: result }));
    } catch (err) {
      console.error(`Error in ${functionName}:`, err);
      setDemoResults(prev => ({
        ...prev,
        [functionName]: { error: err.message || `Failed to execute ${functionName}` }
      }));
    } finally {
      setDemoLoading(prev => ({ ...prev, [functionName]: false }));
    }
  };

  // Styles for the professional landing page
  const styles = {
    container: {
      minHeight: '100vh',
      background: 'url("/images/bg.jpg")',
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      backgroundAttachment: 'fixed',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px',
      fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
    },
    header: {
      textAlign: 'center',
      marginBottom: '50px',
      color: 'white'
    },
    logo: {
      width: '200px',
      height: 'auto',
      marginBottom: '20px',
      filter: 'drop-shadow(0 4px 8px rgba(0,0,0,0.3))'
    },
    title: {
      fontSize: '2.5rem',
      fontWeight: '300',
      margin: '0 0 10px 0',
      textShadow: '2px 2px 4px rgba(0,0,0,0.5)'
    },
    subtitle: {
      fontSize: '1.2rem',
      fontWeight: '300',
      opacity: 0.9,
      textShadow: '1px 1px 2px rgba(0,0,0,0.5)'
    },
    buttonGrid: {
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
      gap: '20px',
      maxWidth: '800px',
      width: '100%'
    },
    demoButton: {
      background: 'rgba(255, 255, 255, 0.1)',
      backdropFilter: 'blur(10px)',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      borderRadius: '15px',
      padding: '25px',
      color: 'white',
      cursor: 'pointer',
      transition: 'all 0.3s ease',
      textAlign: 'center',
      minHeight: '120px',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center'
    },
    demoButtonHover: {
      background: 'rgba(255, 255, 255, 0.2)',
      transform: 'translateY(-5px)',
      boxShadow: '0 10px 25px rgba(0,0,0,0.3)'
    },
    buttonTitle: {
      fontSize: '1.3rem',
      fontWeight: '600',
      marginBottom: '8px'
    },
    buttonDescription: {
      fontSize: '0.9rem',
      opacity: 0.8,
      lineHeight: '1.4'
    },
    resultModal: {
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0,0,0,0.8)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '20px'
    },
    resultContent: {
      background: 'white',
      borderRadius: '15px',
      padding: '30px',
      maxWidth: '600px',
      maxHeight: '80vh',
      overflow: 'auto',
      position: 'relative'
    },
    closeButton: {
      position: 'absolute',
      top: '15px',
      right: '20px',
      background: 'none',
      border: 'none',
      fontSize: '24px',
      cursor: 'pointer',
      color: '#666'
    }
  };

  // Demo functions configuration
  const demoFunctions = [
    {
      name: 'initEkyc',
      title: 'Initialize eKYC',
      description: 'Initialize the eKYC SDK with session token and configuration'
    },
    {
      name: 'ocrIdCard',
      title: 'Scan & OCR ID',
      description: 'Scan and extract data from ID cards using OCR technology'
    },
    {
      name: 'ocrIdCardVerifyByFace',
      title: 'Face & ID Verification',
      description: 'Combine ID card OCR with facial biometric verification'
    },
    {
      name: 'ndidVerification',
      title: 'NDID Verification',
      description: 'Perform NDID (National Digital ID) verification process'
    },
    {
      name: 'livenessCheck',
      title: 'Liveness',
      description: 'Verify that the person is physically present (anti-spoofing)'
    }
  ];

  return (
    <>
      <Head>
        <title>IdentityX - eKYC SDK by SCB TechX</title>
        <meta name="description" content="Professional eKYC SDK with FaceTec integration for identity verification" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div style={styles.container}>
        {/* Header Section */}
        <div style={styles.header}>
          <img
            src="/images/logo.png"
            alt="IdentityX Logo"
            style={styles.logo}
          />
          <h1 style={styles.title}>IdentityX</h1>
          <p style={styles.subtitle}>
            Professional eKYC SDK with Advanced Identity Verification
          </p>
        </div>

        {/* Error Display */}
        {error && (
          <div style={{
            background: 'rgba(255, 0, 0, 0.1)',
            border: '1px solid rgba(255, 0, 0, 0.3)',
            borderRadius: '10px',
            padding: '15px',
            color: 'white',
            marginBottom: '30px',
            textAlign: 'center'
          }}>
            <p>{error}</p>
          </div>
        )}

        {/* Demo Functions Grid */}
        {lib && (
          <div style={styles.buttonGrid}>
            {demoFunctions.map((func) => (
              <div
                key={func.name}
                style={{
                  ...styles.demoButton,
                  ...(demoLoading[func.name] ? { opacity: 0.7, cursor: 'not-allowed' } : {})
                }}
                onClick={() => !demoLoading[func.name] && handleDemoFunction(func.name)}
                onMouseEnter={(e) => {
                  if (!demoLoading[func.name]) {
                    Object.assign(e.target.style, styles.demoButtonHover);
                  }
                }}
                onMouseLeave={(e) => {
                  if (!demoLoading[func.name]) {
                    Object.assign(e.target.style, styles.demoButton);
                  }
                }}
              >
                <div style={styles.buttonTitle}>
                  {demoLoading[func.name] ? 'Processing...' : func.title}
                </div>
                <div style={styles.buttonDescription}>
                  {func.description}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Loading State */}
        {!lib && !error && (
          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            borderRadius: '15px',
            padding: '30px',
            color: 'white',
            textAlign: 'center'
          }}>
            <p style={{ fontSize: '1.2rem', margin: 0 }}>Loading SDK...</p>
          </div>
        )}

        {/* Results Modal */}
        {activeDemo && demoResults[activeDemo] && (
          <div style={styles.resultModal} onClick={() => setActiveDemo(null)}>
            <div style={styles.resultContent} onClick={(e) => e.stopPropagation()}>
              <button
                style={styles.closeButton}
                onClick={() => setActiveDemo(null)}
              >
                ×
              </button>
              <h2>Result: {demoFunctions.find(f => f.name === activeDemo)?.title}</h2>
              <pre style={{
                background: '#f5f5f5',
                padding: '15px',
                borderRadius: '8px',
                overflow: 'auto',
                maxHeight: '400px',
                fontSize: '14px'
              }}>
                {JSON.stringify(demoResults[activeDemo], null, 2)}
              </pre>
            </div>
          </div>
        )}
      </div>
    </>
  );
}
