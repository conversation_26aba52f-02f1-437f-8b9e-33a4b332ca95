# Callback Structure Customization Summary

## Overview
This document summarizes the changes made to ensure that callback results in `simple.js` and `PhotoIDMatchProcessor.js` always have the standardized structure as requested.

## Required Callback Structure
```javascript
{
  success: callbackResult ? callbackResult.success : true,
  description: callbackResult ? callbackResult.description : "OCR ID Card with face verification completed successfully",
  userOcrValue: extractOcrFromApiResponse from PhotoMatchIdProcessor,
  userConfirmedValue: null,
  dopaResult: null
}
```

## Changes Made

### 1. PhotoIDMatchProcessor.js

#### Modified Callback Result Creation
- **Success Case** (lines 178-190): Updated to use standardized structure
  ```javascript
  _this.callbackResult = {
    success: true,
    description: "OCR ID Card with face verification completed successfully",
    userOcrValue: extractedOcrValue,
    userConfirmedValue: null,
    dopaResult: null
  };
  ```

- **Error Case** (lines 210-221): Updated to use standardized structure
  ```javascript
  _this.callbackResult = {
    success: false,
    description: result.errorMessage || "OCR ID Card with face verification failed",
    userOcrValue: extractedOcrValue,
    userConfirmedValue: null,
    dopaResult: null
  };
  ```

- **Exception Case** (lines 232-242): Updated to use standardized structure
  ```javascript
  _this.callbackResult = {
    success: false,
    description: "Network error during OCR ID Card with face verification: " + error.message,
    userOcrValue: null,
    userConfirmedValue: null,
    dopaResult: null
  };
  ```

- **User Confirmation Case** (lines 575-586): Updated to use standardized structure
  ```javascript
  _this.callbackResult = {
    success: true,
    description: "OCR ID Card with face verification completed successfully",
    userOcrValue: originalOcrValue,
    userConfirmedValue: userOcrValue,
    dopaResult: null
  };
  ```

#### Improved OCR Extraction
- **extractOcrValueForCallback** (lines 624-644): Simplified to delegate to `extractOcrFromApiResponse`
- **extractOcrFromApiResponse** (lines 646-697): Enhanced to handle multiple input formats:
  - Priority 1: Use existing `userOcrValue` if present
  - Priority 2: Extract from `originalResponse.data.documentData`
  - Priority 3: Process `documentData` directly

### 2. simple.js

#### Updated performPhotoIDMatch Function
- **Added ocrResultsCallback parameter** (line 217): Now accepts callback parameter
- **Pass callback to processor** (line 250): Ensures processor receives callback
- **Return processor in result** (lines 262, 266): Allows access to callback results

#### Updated ocrIdCardVerifyByFace Function
- **Enhanced OCR extraction** (lines 530-548): Added logic to extract userOcrValue using PhotoIDMatchProcessor's method
- **Standardized result structure** (lines 541-559): Always returns the required structure
- **Removed legacy properties** (lines 578-592): Cleaned up error result structure

## Key Features

### 1. Consistent Structure
All callback results now have exactly these 5 properties:
- `success`: boolean
- `description`: string  
- `userOcrValue`: object | null
- `userConfirmedValue`: object | null
- `dopaResult`: object | null

### 2. OCR Data Extraction
The `extractOcrFromApiResponse` method handles various input formats:
- API responses with `documentData`
- Results with existing `userOcrValue`
- Nested `originalResponse` structures
- Thai date format conversion (e.g., "01 ม.ค. 1990" → "01/01/1990")

### 3. Error Handling
All error scenarios maintain the same callback structure:
- Network errors
- API failures
- Validation errors
- User cancellations

### 4. User Confirmation
When users confirm information through the overlay:
- `userOcrValue`: Original OCR data from API
- `userConfirmedValue`: User-confirmed data from overlay
- Maintains consistent structure

## Testing

### Demo Script
Created `simple-callback-demo.js` to verify:
- OCR data extraction works correctly
- Callback structure is consistent
- All required properties are present
- Property types are correct

### Test Results
```
✅ Success result validation: { isValid: true, hasAllProperties: true, hasCorrectTypes: true }
✅ Error result validation: { isValid: true, hasAllProperties: true, hasCorrectTypes: true }
```

## Usage Example

```javascript
// Using ocrIdCardVerifyByFace with callback
const result = await ocrIdCardVerifyByFace({
  checkExpiredIdCard: true,
  checkDopa: false,
  enableConfirmInfo: true,
  callback: (callbackResult) => {
    console.log('Callback result:', callbackResult);
    // Always has the standardized structure:
    // {
    //   success: boolean,
    //   description: string,
    //   userOcrValue: object | null,
    //   userConfirmedValue: object | null,
    //   dopaResult: object | null
    // }
  }
});

// Return value also has the same structure
console.log('Return value:', result);
```

## Benefits

1. **Predictable API**: External applications can rely on consistent callback structure
2. **Type Safety**: Clear property types enable better TypeScript integration
3. **Maintainability**: Centralized structure definition reduces code duplication
4. **Debugging**: Consistent structure makes debugging easier
5. **Documentation**: Clear contract for callback parameters

## Files Modified

1. `lib/processors/PhotoIDMatchProcessor.js`
2. `lib/simple.js`
3. `simple-callback-demo.js` (demo/test file)
4. `CALLBACK-STRUCTURE-SUMMARY.md` (this documentation)

All changes maintain backward compatibility while ensuring the callback structure is always consistent and follows the specified format.
