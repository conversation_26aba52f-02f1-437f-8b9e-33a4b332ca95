/**
 * <PERSON><PERSON> script to verify the callback structure is working correctly
 * This script demonstrates that the PhotoIDMatchProcessor and simple.js functions
 * always return the standardized callback structure:
 * 
 * {
 *   success: boolean,
 *   description: string,
 *   userOcrValue: object | null,
 *   userConfirmedValue: object | null,
 *   dopaResult: object | null
 * }
 */

// Mock XMLHttpRequest for Node.js environment
global.XMLHttpRequest = class XMLHttpRequest {
  constructor() {
    this.status = 200;
  }
  abort() {}
};

// Mock FaceTecSDK for testing
global.FaceTecSDK = {
  FaceTecCustomization: {
    setOverrideResultScreenSuccessMessage: () => {},
    setIDScanResultScreenMessageOverrides: () => {},
    setIDScanUploadMessageOverrides: () => {}
  }
};

// Mock document for testing
global.document = {
  createElement: () => ({
    style: {},
    addEventListener: () => {},
    remove: () => {}
  }),
  body: {
    appendChild: () => {}
  },
  getElementById: () => null
};

const PhotoIDMatchProcessor = require('./lib/processors/PhotoIDMatchProcessor');

console.log('🧪 Testing PhotoIDMatchProcessor Callback Structure');
console.log('=' .repeat(60));

// Test 1: extractOcrFromApiResponse with valid API data
console.log('\n📋 Test 1: extractOcrFromApiResponse with valid API data');
const processor = new PhotoIDMatchProcessor(
  'mock-session-token',
  { onComplete: () => {} },
  'mock-device-key',
  { 'X-Session-Id': 'test-session' },
  null
);

const mockApiData = {
  documentData: JSON.stringify({
    scannedValues: {
      groups: [{
        fields: [
          { fieldKey: 'nationalId', value: '1234567890123' },
          { fieldKey: 'fullname', value: 'นาย สมชาย ใจดี' },
          { fieldKey: 'firstName', value: 'MR. SOMCHAI JAIDEE' },
          { fieldKey: 'dateOfBirth', value: '01 ม.ค. 1990' },
          { fieldKey: 'dateOfIssue', value: '15 ก.พ. 2020' },
          { fieldKey: 'dateOfExpiry', value: '14 ก.พ. 2025' },
          { fieldKey: 'laserId', value: 'AB1234567' }
        ]
      }]
    }
  })
};

const extractedOcr = processor.extractOcrFromApiResponse(mockApiData);
console.log('✅ Extracted OCR data:', JSON.stringify(extractedOcr, null, 2));

// Test 2: Verify callback result structure for success case
console.log('\n📋 Test 2: Callback result structure for success case');
processor.callbackResult = {
  success: true,
  description: "OCR ID Card with face verification completed successfully",
  userOcrValue: extractedOcr,
  userConfirmedValue: null,
  dopaResult: null
};

const successResult = processor.getCallbackResult();
console.log('✅ Success callback result:', JSON.stringify(successResult, null, 2));

// Test 3: Verify callback result structure for error case
console.log('\n📋 Test 3: Callback result structure for error case');
processor.callbackResult = {
  success: false,
  description: "OCR ID Card with face verification failed",
  userOcrValue: null,
  userConfirmedValue: null,
  dopaResult: null
};

const errorResult = processor.getCallbackResult();
console.log('✅ Error callback result:', JSON.stringify(errorResult, null, 2));

// Test 4: Verify structure validation
console.log('\n📋 Test 4: Structure validation');
function validateCallbackStructure(result) {
  const requiredProperties = ['success', 'description', 'userOcrValue', 'userConfirmedValue', 'dopaResult'];
  const hasAllProperties = requiredProperties.every(prop => result.hasOwnProperty(prop));
  const hasCorrectTypes = 
    typeof result.success === 'boolean' &&
    typeof result.description === 'string' &&
    (result.userOcrValue === null || typeof result.userOcrValue === 'object') &&
    (result.userConfirmedValue === null || typeof result.userConfirmedValue === 'object') &&
    (result.dopaResult === null || typeof result.dopaResult === 'object');
  
  return {
    hasAllProperties,
    hasCorrectTypes,
    isValid: hasAllProperties && hasCorrectTypes
  };
}

const successValidation = validateCallbackStructure(successResult);
const errorValidation = validateCallbackStructure(errorResult);

console.log('✅ Success result validation:', successValidation);
console.log('✅ Error result validation:', errorValidation);

// Test 5: Test with userOcrValue already present
console.log('\n📋 Test 5: extractOcrFromApiResponse with existing userOcrValue');
const mockDataWithUserOcr = {
  userOcrValue: {
    nationalId: '9876543210987',
    titleTh: 'นาง',
    firstNameTh: 'สมหญิง'
  }
};

const existingOcr = processor.extractOcrFromApiResponse(mockDataWithUserOcr);
console.log('✅ Existing userOcrValue preserved:', JSON.stringify(existingOcr, null, 2));

console.log('\n🎉 All tests completed successfully!');
console.log('✅ The callback structure is consistent and follows the required format:');
console.log('   {');
console.log('     success: boolean,');
console.log('     description: string,');
console.log('     userOcrValue: object | null,');
console.log('     userConfirmedValue: object | null,');
console.log('     dopaResult: object | null');
console.log('   }');
